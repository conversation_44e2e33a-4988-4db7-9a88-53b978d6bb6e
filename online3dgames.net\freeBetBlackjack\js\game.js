$(document).ready(function() {
    let effectsMuted = false;
    let effectsVolume = 0.5;
    let dealSound = null;

    window.addEventListener('languageChanged', function(event) {
        updateGameTexts();
    });

    function updateGameTexts() {
        if (window.i18n) {
            const currentStatus = $('#game-status').text();

            if (currentStatus.includes('place your bet') || currentStatus.includes('Setzen Sie')) {
                updateGameStatus(window.i18n.t('ui.gameStatus'));
            } else if (currentStatus.includes('Hit or Stand') || currentStatus.includes('Karte oder Halten')) {
                updateGameStatus(window.i18n.t('messages.yourTurn'));
            } else if (currentStatus.includes('Click Done') || currentStatus.includes('Klicken Sie')) {
                updateGameStatus(window.i18n.t('messages.clickDoneToStart'));
            } else if (currentStatus.includes('Welcome') || currentStatus.includes('Willkommen')) {
                updateGameStatus(window.i18n.t('messages.welcomePlaceBet'));
            } else if (currentStatus.includes('Shuffling') || currentStatus.includes('gemischt')) {
                updateGameStatus(window.i18n.t('messages.shuffling'));
            } else if (currentStatus.includes('Calculating') || currentStatus.includes('berechnet')) {
                updateGameStatus(window.i18n.t('messages.calculatingResults'));
            } else if (currentStatus.includes('Free Split') || currentStatus.includes('Gratis')) {
                if (currentStatus.includes('used') || currentStatus.includes('verwendet')) {
                    updateGameStatus(window.i18n.t('messages.freeSplitAvailable'));
                }
            } else if (currentStatus.includes('Free Double') || currentStatus.includes('Gratis Verdoppeln')) {
                updateGameStatus(window.i18n.t('messages.freeDoubleAvailable'));
            } else if (currentStatus.includes('Basic strategy') || currentStatus.includes('Grundstrategie') || currentStatus.includes('Free Bet strategy')) {
                if (currentStatus.includes('Hit') && !currentStatus.includes('Stand')) {
                    updateGameStatus(window.i18n.t('hints.hit'));
                } else if (currentStatus.includes('Stand')) {
                    updateGameStatus(window.i18n.t('hints.stand'));
                } else if (currentStatus.includes('Double')) {
                    if (currentStatus.includes('not available') || currentStatus.includes('nicht verfügbar')) {
                        updateGameStatus(window.i18n.t('hints.doubleNotAvailable'));
                    } else if (currentStatus.includes('Free') || currentStatus.includes('Gratis')) {
                        updateGameStatus(window.i18n.t('hints.freeDouble'));
                    } else {
                        updateGameStatus(window.i18n.t('hints.double'));
                    }
                } else if (currentStatus.includes('Split')) {
                    if (currentStatus.includes('not available') || currentStatus.includes('nicht verfügbar')) {
                        updateGameStatus(window.i18n.t('hints.splitNotAvailable'));
                    } else if (currentStatus.includes('Free') || currentStatus.includes('Gratis')) {
                        updateGameStatus(window.i18n.t('hints.freeSplit'));
                    } else {
                        updateGameStatus(window.i18n.t('hints.split'));
                    }
                }
            }
        }
    }

    try {
        dealSound = new Audio('/freeBetBlackjack/audio/deal.mp3');
        dealSound.preload = 'auto';
        dealSound.volume = effectsVolume;
    } catch (error) {}

    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover');
    }

    // iOS device detection and fullscreen tip
    function showIOSTip() {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isStandalone = window.navigator.standalone || window.matchMedia('(display-mode: standalone)').matches;

        if (isIOS && !isStandalone) {
            // Only show tip for iOS devices not in PWA mode
            console.log('iOS device detected - for best fullscreen experience, add to home screen');
        }
    }

    // Handle mobile fullscreen coverage only when in actual fullscreen mode
    function ensureFullScreenCoverage() {
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isFullscreen = !!(document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement);

        if (isMobile && isFullscreen) {
            // Force viewport dimensions only in fullscreen
            $('html, body').css({
                'width': '100vw',
                'height': '100vh',
                'margin': '0',
                'padding': '0',
                'overflow-x': 'hidden'
            });

            if (isIOS) {
                const isStandalone = window.navigator.standalone || window.matchMedia('(display-mode: standalone)').matches;
                if (isStandalone) {
                    $('body').css({
                        'padding-top': '0',
                        'height': '100vh'
                    });
                } else {
                    $('body').css({
                        'height': '100vh',
                        'min-height': '-webkit-fill-available'
                    });
                }
            }
        } else if (isMobile && !isFullscreen) {
            // Reset styles when not in fullscreen to allow normal scrolling
            $('html, body').css({
                'width': '',
                'height': '',
                'margin': '',
                'padding': '',
                'overflow-x': ''
            });

            $('body').css({
                'padding-top': '',
                'height': '',
                'min-height': ''
            });
        }
    }

    showIOSTip();

    function loadAudioSettings() {
        try {
            const savedEffectsVolume = localStorage.getItem('blackjack-effects-volume');
            const savedEffectsMuted = localStorage.getItem('blackjack-effects-muted');
            if (savedEffectsVolume !== null) {
                effectsVolume = parseFloat(savedEffectsVolume);
            }
            if (savedEffectsMuted !== null) {
                effectsMuted = savedEffectsMuted === 'true';
            }
            updateAudioVolumes();
        } catch (error) {}
    }

    function updateAudioVolumes() {
        if (dealSound) {
            dealSound.volume = effectsMuted ? 0 : effectsVolume;
        }
    }

    loadAudioSettings();

    const domCache = {};

    const activeTimers = {
        timeouts: new Set(),
        intervals: new Set(),
        animationFrames: new Set()
    };

    function safeSetTimeout(callback, delay) {
        const timeoutId = setTimeout(() => {
            activeTimers.timeouts.delete(timeoutId);
            callback();
        }, delay);
        activeTimers.timeouts.add(timeoutId);
        return timeoutId;
    }

    function clearAllTimers() {
        activeTimers.timeouts.forEach(id => clearTimeout(id));
        activeTimers.intervals.forEach(id => clearInterval(id));
        activeTimers.animationFrames.forEach(id => cancelAnimationFrame(id));
        activeTimers.timeouts.clear();
        activeTimers.intervals.clear();
        activeTimers.animationFrames.clear();
    }

    function cleanupAnimationElements() {
        $('.flying-chip').remove();
        $('[class*="animate-"]').removeClass(function(_, className) {
            return (className.match(/(^|\s)animate-\S+/g) || []).join(' ');
        });
    }

    function initDOMCache() {
        domCache.gameStatus = document.getElementById('game-status');
        domCache.bettingControls = document.getElementById('betting-controls');
        domCache.actionControls = document.getElementById('action-controls');
        domCache.dealCards = document.getElementById('deal-cards');
        domCache.deckCount = document.getElementById('deck-count');
    }
    let gameSettings = {
        playerCount: 1,
        deckCount: 1,
        pendingDeckCount: null
    };

    function getPlayerHtmlPosition(playerIndex) {
        return gameSettings.playerCount === 1 ? 0 : playerIndex;
    }

    let gameState = {
        deck: [],
        discardPile: [],
        totalCards: 0,
        dealerCards: [],
        dealerScore: 0,
        players: [],
        currentPlayerIndex: 0,
        balance: 1000,
        balanceCache: 1000,
        gameInProgress: false,
        dealerHiddenCard: null,
        currentTurnIndex: -1,
        gamePhase: 'waiting',
        gameStarted: false,
        isShuffling: false,
        gameOverModalShown: false,
        insuranceOffered: false,
        dealerHasBlackjack: false,
        freeBetActive: false,
        dealer22Push: false,
        hasAutoFullscreened: false
    };

    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const suitColors = {'♠': 'black', '♣': 'black', '♥': 'red', '♦': 'red'};
    function initializePlayers() {
        gameState.players = [];
        for (let i = 0; i < gameSettings.playerCount; i++) {
            const player = {
                cards: [],
                score: 0,
                bet: 0,
                isActive: false,
                name: 'You',
                isBust: false,
                avatar: '/freeBetBlackjack/images/user.png',
                splitHands: [],
                currentHandIndex: 0,
                hasInsurance: false,
                insuranceBet: 0,
                hasFreeBet: false,
                freeBetAmount: 0,
                canFreeDouble: false,
                canFreeSplit: false,
                usedFreeDouble: false,
                usedFreeSplit: false
            };
            gameState.players.push(player);
        }
        gameState.currentPlayerIndex = 0;
    }

    function updatePlayerPositionsDisplay() {
        $('.player-position').hide();
        $('.players-area').removeClass('player-count-1 player-count-3 player-count-4 player-count-6');
        $('.players-area').addClass(`player-count-${gameSettings.playerCount}`);

        for (let i = 0; i < gameSettings.playerCount; i++) {
            const htmlPosition = getPlayerHtmlPosition(i);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (playerPosition.length > 0) {
                playerPosition.show();
                const player = gameState.players[i];
                if (player) {
                    const avatarImg = playerPosition.find('.player-avatar img');
                    const playerName = playerPosition.find('.player-name');
                    if (avatarImg.length > 0) {
                        avatarImg.attr('src', player.avatar);
                        avatarImg.attr('alt', player.name);
                    }
                    if (playerName.length > 0) {
                        playerName.text(player.name);
                    }
                    playerPosition.addClass('current-player');
                    playerPosition.find('.player-balance').show();
                }
            }
        }
    }
    function initGame() {
        loadDeckSettings();
        initializePlayers();
        createDeck();
        shuffleDeck();
        updateDisplay();
        hideAllPlayerScores();
        updateChipDenominations();
        bindEvents();
        startGame();
    }
    function createDeck() {
        gameState.deck = [];
        gameState.discardPile = [];

        for (let deckNum = 0; deckNum < gameSettings.deckCount; deckNum++) {
            for (let suit of suits) {
                for (let value of values) {
                    gameState.deck.push({
                        suit: suit,
                        value: value,
                        color: suitColors[suit]
                    });
                }
            }
        }

        gameState.totalCards = gameState.deck.length;
        updateDeckDisplay();
    }

    function updateDeckDisplay() {
        const remainingCards = gameState.deck.length;
        const discardedCards = gameState.discardPile.length;
        const totalCards = gameState.totalCards;

        $('#deck-count').text(remainingCards);
        $('#discard-count').text(discardedCards);

        const deckProgress = totalCards > 0 ? (remainingCards / totalCards) * 100 : 0;
        const discardProgress = totalCards > 0 ? (discardedCards / totalCards) * 100 : 0;

        $('#deck-progress-fill').css('width', `${deckProgress}%`);
        $('#discard-progress-fill').css('width', `${discardProgress}%`);

        const deckCard = $('#main-deck .deck-card');
        if (remainingCards === 0) {
            deckCard.css('opacity', '0.3');
        } else if (remainingCards < totalCards * 0.2) {
            deckCard.css('opacity', '0.6');
        } else {
            deckCard.css('opacity', '1');
        }
    }





    function shuffleDeck() {
        if (gameState.deck.length === 0) return;

        const shufflePasses = 3 + Math.floor(Math.random() * 3);
        for (let pass = 0; pass < shufflePasses; pass++) {
            for (let i = gameState.deck.length - 1; i > 0; i--) {
                let randomValue;
                if (window.crypto && window.crypto.getRandomValues) {
                    const array = new Uint32Array(1);
                    window.crypto.getRandomValues(array);
                    randomValue = array[0] / (0xFFFFFFFF + 1);
                } else {
                    randomValue = Math.random();
                }
                const j = Math.floor(randomValue * (i + 1));
                [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
            }

            if (pass < shufflePasses - 1) {
                riffleShuffle();
            }
        }
        updateDeckDisplay();
    }

    function riffleShuffle() {
        const deckSize = gameState.deck.length;
        if (deckSize < 2) return;

        const splitPoint = Math.floor(deckSize / 2) + Math.floor(Math.random() * 6) - 3;
        const leftHalf = gameState.deck.slice(0, splitPoint);
        const rightHalf = gameState.deck.slice(splitPoint);

        const newDeck = [];
        let leftIndex = 0;
        let rightIndex = 0;

        while (leftIndex < leftHalf.length || rightIndex < rightHalf.length) {
            const takeFromLeft = leftIndex < leftHalf.length &&
                (rightIndex >= rightHalf.length || Math.random() < 0.5);
            if (takeFromLeft) {
                newDeck.push(leftHalf[leftIndex++]);
            } else {
                newDeck.push(rightHalf[rightIndex++]);
            }
        }

        if (newDeck.length === deckSize) {
            gameState.deck = newDeck;
        }
    }
    function dealCard() {
        if (gameState.deck.length === 0) {
            triggerShuffleSync();
        }
        const card = gameState.deck.pop();
        if (!card) {
            triggerShuffleSync();
            return gameState.deck.pop();
        }
        updateDeckDisplay();
        return card;
    }

    function triggerShuffleSync() {
        if (gameState.isShuffling) return;
        gameState.isShuffling = true;

        gameState.deck = [...gameState.deck, ...gameState.discardPile];
        gameState.discardPile = [];
        shuffleDeck();
        gameState.isShuffling = false;
    }

    function triggerShuffleWithAnimation() {
        if (gameState.isShuffling) {
            return Promise.resolve();
        }
        gameState.isShuffling = true;
        updateGameStatus(window.i18n ? window.i18n.t('messages.shuffling') : 'Shuffling...');
        $('.deck-card').addClass('shuffling');

        return new Promise(resolve => {
            safeSetTimeout(() => {
                gameState.deck = [...gameState.deck, ...gameState.discardPile];
                gameState.discardPile = [];
                shuffleDeck();
                $('.deck-card').removeClass('shuffling');
                gameState.isShuffling = false;
                updateDeckDisplay();
                resolve();
            }, 2000);
        });
    }
    function calculateScore(cards) {
        if (!cards || !Array.isArray(cards)) {
            return 0;
        }
        let score = 0;
        let aces = 0;
        for (let card of cards) {
            if (!card || !card.value) {
                continue;
            }
            if (card.value === 'A') {
                aces++;
                score += 11;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                score += 10;
            } else {
                score += parseInt(card.value);
            }
        }
        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }
        return score;
    }
    function getScoreDisplay(cards) {
        if (!cards || !Array.isArray(cards)) {
            return '0';
        }
        let hardScore = 0;
        let aces = 0;
        for (let card of cards) {
            if (!card || !card.value) {
                continue;
            }
            if (card.value === 'A') {
                aces++;
                hardScore += 1;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                hardScore += 10;
            } else {
                hardScore += parseInt(card.value);
            }
        }
        if (aces === 0) {
            return hardScore.toString();
        }
        let softScore = hardScore + 10; 
        if (softScore === 21) {
            return '21';
        }
        if (softScore > 21) {
            return hardScore.toString();
        }
        if (hardScore === softScore) {
            return hardScore.toString();
        }
        return `${hardScore}/${softScore}`;
    }
    function canPlayerSplit(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.cards.length !== 2 || player.splitHands.length > 0) {
            return false;
        }
        if (!player.cards[0] || !player.cards[1]) {
            return false;
        }
        const card1Value = player.cards[0].value;
        const card2Value = player.cards[1].value;
        const isSameValue = card1Value === card2Value;

        // For 10-value cards, need enough balance for paid split
        const is10ValueCard = ['10', 'J', 'Q', 'K'].includes(card1Value);
        if (is10ValueCard) {
            const hasEnoughBalance = player.isAI || gameState.balance >= player.bet;
            return isSameValue && hasEnoughBalance;
        }

        // For A-9, always allow split (will be free)
        return isSameValue;
    }
    function splitPlayerHand(playerIndex) {
        const player = gameState.players[playerIndex];
        if (!canPlayerSplit(playerIndex)) {
            return false;
        }
        // Check if this is a free split (A-9 pairs) or if player used Free Split button
        const card1Value = player.cards[0].value;
        const isEligibleForFreeSplit = !['10', 'J', 'Q', 'K'].includes(card1Value);
        const isActualFreeSplit = player.hasFreeBet && player.usedFreeSplit;

        if (!player.isAI && !isEligibleForFreeSplit && !isActualFreeSplit) {
            // Only deduct balance for paid splits (10-value cards when not using Free Split)
            if (gameState.balance < player.bet) {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalanceForSplit') : 'Insufficient balance for split!');
                return false;
            }
            gameState.balance -= player.bet;
        }
        animateSplitBetChips(player.bet, playerIndex);
        const firstCard = player.cards[0];
        const secondCard = player.cards[1];
        player.splitHands = [
            {
                cards: [firstCard],
                score: 0,
                bet: player.bet,
                isBust: false,
                isComplete: false,
                hasFreeBet: isActualFreeSplit,
                freeBetAmount: isActualFreeSplit ? player.bet : 0
            },
            {
                cards: [secondCard],
                score: 0,
                bet: player.bet,
                isBust: false,
                isComplete: false,
                hasFreeBet: isActualFreeSplit,
                freeBetAmount: isActualFreeSplit ? player.bet : 0
            }
        ];
        player.cards = [];
        player.currentHandIndex = 0;
        player.splitHands[0].score = calculateScore(player.splitHands[0].cards);
        player.splitHands[1].score = calculateScore(player.splitHands[1].cards);
        playSound('deal');
        updateDisplay();
        safeSetTimeout(() => {
            updateBetDisplay(playerIndex, player.bet * 2);
        }, 200);
        performSplitAnimation(playerIndex, () => {
            displaySplitHands(playerIndex, -1);
            safeSetTimeout(() => {
                player.splitHands[0].cards.push(dealCard());
                player.splitHands[0].score = calculateScore(player.splitHands[0].cards);
                playSound('deal');
                displaySplitHands(playerIndex, 1);
            }, 300);
        });
        const totalBetAfterSplit = player.bet * 2;
        if (isFreeSplit) {
            const message = window.i18n ?
                window.i18n.t('messages.handHitOrStand', { handNumber: 1 }) :
                'Free Split used! Second hand is free - Hand 1 - Hit or Stand?';
            showGameStatus(message);
            showFreeBetStatus(window.i18n ? window.i18n.t('messages.freeSplitAvailable') : 'Free Split activated - you only risk your original bet!');
        } else {
            const message = window.i18n ?
                window.i18n.t('messages.playerChoseToSplit', { amount: totalBetAfterSplit.toLocaleString() }) :
                `Player chose to split! Total bet: ${totalBetAfterSplit.toLocaleString()} - Hand 1 - Hit or Stand?`;
            showGameStatus(message);
        }
        return true;
    }
    function moveToNextSplitHand(playerIndex, callback) {
        const player = gameState.players[playerIndex];
        if (player.splitHands.length === 0) {
            if (callback) callback(false);
            return false;
        }
        player.splitHands[player.currentHandIndex].isComplete = true;
        const currentHand = player.splitHands[player.currentHandIndex];
        const delay = currentHand.isBust ? 1000 : 0;
        safeSetTimeout(() => {
            player.currentHandIndex++;
            if (player.currentHandIndex < player.splitHands.length) {
                performSplitHandTransition(playerIndex, () => {
                    displaySplitHands(playerIndex, -1);
                    const nextHand = player.splitHands[player.currentHandIndex];
                    let newCardIndex = -1;
                    if (nextHand.cards.length === 1) {
                        nextHand.cards.push(dealCard());
                        nextHand.score = calculateScore(nextHand.cards);
                        newCardIndex = 1;
                        playSound('deal');
                        displaySplitHands(playerIndex, newCardIndex);
                    }
                    const message = window.i18n ?
                        window.i18n.t('messages.handHitOrStand', { handNumber: player.currentHandIndex + 1 }) :
                        `Hand ${player.currentHandIndex + 1} - Hit or Stand?`;
                    updateGameStatus(message);
                    updateSplitButtonVisibility();
                    if (callback) callback(true);
                });
                return true;
            } else {
                displaySplitHandsFinal(playerIndex);
                if (callback) callback(false);
                return false;
            }
        }, delay);
        return true;
    }
    function performSplitAnimation(playerIndex, callback) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const cards = cardsContainer.find('.card');
        if (cards.length >= 2) {
            const secondCard = cards.eq(1);
            cards.removeClass('dealt').css('animation', 'none');
            const animatedCard = secondCard.clone();
            animatedCard.removeClass('dealt');
            animatedCard.addClass('splitting-card');
            animatedCard.css({
                position: 'absolute',
                top: secondCard.position().top,
                left: secondCard.position().left,
                zIndex: 100,
                animation: 'none'
            });
            cardsContainer.append(animatedCard);
            secondCard.css('opacity', '0');
            const tempMiniHand = $('<div class="mini-hand" style="visibility: hidden;"></div>');
            cardsContainer.append(tempMiniHand);
            const miniPosition = tempMiniHand.position();
            const miniTargetLeft = miniPosition.left;
            const miniTargetTop = miniPosition.top;
            tempMiniHand.remove();
            animatedCard.animate({
                left: miniTargetLeft + 'px',
                top: miniTargetTop + 'px'
            }, {
                duration: 300, 
                easing: 'swing',
                step: function(now, fx) {
                    if (fx.prop === 'left') {
                        const progress = Math.abs(now - fx.start) / Math.abs(fx.end - fx.start);
                        const scale = 1 - (progress * 0.5);
                        $(this).css({
                            'transform': `scale(${scale})`,
                            'transform-origin': 'top left'
                        });
                    }
                },
                complete: function() {
                    $(this).css({
                        'transform': 'scale(0.5)',
                        'transform-origin': 'top left',
                        'left': miniTargetLeft + 'px',
                        'top': miniTargetTop + 'px'
                    });
                    safeSetTimeout(() => {
                        animatedCard.remove();
                        callback();
                    }, 50);
                }
            });
        } else {
            safeSetTimeout(callback, 100);
        }
    }
    function performSplitHandTransition(playerIndex, callback) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const splitContainer = cardsContainer.find('.split-hands-container');
        if (splitContainer.length === 0) {
            safeSetTimeout(callback, 500);
            return;
        }
        splitContainer.addClass('transitioning');
        safeSetTimeout(() => {
            splitContainer.removeClass('transitioning');
            callback();
        }, 500);
    }
    function displaySplitHands(playerIndex, newCardIndex = -1) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        let splitContainer = cardsContainer.find('.split-hands-container');
        if (splitContainer.length === 0) {
            cardsContainer.empty();
            splitContainer = $('<div class="split-hands-container"></div>');
            cardsContainer.append(splitContainer);
        }
        if (newCardIndex === -1) {
            splitContainer.find('.current-hand, .mini-hand').remove();
        }
        player.splitHands.forEach((hand, handIndex) => {
            if (handIndex === player.currentHandIndex) {
                let currentHandContainer = splitContainer.find('.current-hand');
                if (currentHandContainer.length === 0) {
                    currentHandContainer = $('<div class="current-hand"></div>');
                    splitContainer.append(currentHandContainer);
                }
                if (newCardIndex >= 0 && newCardIndex < hand.cards.length) {
                    const newCard = hand.cards[newCardIndex];
                    if (newCard) {
                        const cardElement = createCardElement(newCard);
                        cardElement.addClass('dealt');
                        currentHandContainer.append(cardElement);
                    }
                } else {
                    currentHandContainer.find('.card').remove(); 
                    hand.cards.forEach((card) => {
                        if (card) {
                            const cardElement = createCardElement(card);
                            cardElement.removeClass('dealt');
                            cardElement.css('animation', 'none');
                            currentHandContainer.append(cardElement);
                        }
                    });
                }
                let currentScore = currentHandContainer.find('.split-hand-score.current-score');
                if (currentScore.length === 0) {
                } else {
                    currentScore.text(getScoreDisplay(hand.cards));
                }
            } else {
                let miniHandContainer = splitContainer.find(`[data-hand="${handIndex}"]`);
                if (miniHandContainer.length === 0) {
                    miniHandContainer = $(`<div class="mini-hand" data-hand="${handIndex}"></div>`);
                    miniHandContainer.css({
                        'position': 'absolute',
                        'top': '-10px',
                        'left': '-80px',
                        'transform': 'scale(0.5)',
                        'transform-origin': 'top left'
                    });
                    splitContainer.append(miniHandContainer);
                    hand.cards.forEach(card => {
                        if (card) {
                            const miniCard = createCardElement(card);
                            miniCard.addClass('mini-card');
                            miniCard.removeClass('dealt');
                            miniCard.css('animation', 'none');
                            miniHandContainer.append(miniCard);
                        }
                    });
                } else {
                    miniHandContainer.find('.card').remove();
                    hand.cards.forEach(card => {
                        if (card) {
                            const miniCard = createCardElement(card);
                            miniCard.addClass('mini-card');
                            miniCard.removeClass('dealt');
                            miniCard.css('animation', 'none');
                            miniHandContainer.append(miniCard);
                        }
                    });
                    miniHandContainer.find('.mini-score').text(getScoreDisplay(hand.cards));
                }
                miniHandContainer.removeClass('completed bust won lost push blackjack twenty-one');
                if (hand.isComplete) {
                    miniHandContainer.addClass('completed');
                    if (hand.isBust) {
                        miniHandContainer.addClass('bust');
                    }
                    if (hand.effectClass) {
                        miniHandContainer.addClass(hand.effectClass);
                    }
                }
            }
        });
        const currentHand = player.splitHands[player.currentHandIndex];
        const htmlPos = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPos}`).text(`Hand ${player.currentHandIndex + 1}: ${getScoreDisplay(currentHand.cards)}`);
    }
    function displaySplitHandsFinal(playerIndex) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        cardsContainer.empty();
        const finalContainer = $('<div class="split-hands-final-container"></div>');
        cardsContainer.append(finalContainer);
        player.splitHands.forEach((hand, handIndex) => {
            const handContainer = $(`<div class="final-hand" data-hand="${handIndex}"></div>`);
            finalContainer.append(handContainer);
            const cardsArea = $('<div class="player-cards-area"></div>');
            handContainer.append(cardsArea);
            hand.cards.forEach(card => {
                const cardElement = createCardElement(card);
                cardsArea.append(cardElement);
            });
            if (hand.isBust) {
                handContainer.addClass('bust');
            }
            if (hand.effectClass) {
                handContainer.addClass(hand.effectClass);
            }
        });
        const hand1Score = player.splitHands[0] ? getScoreDisplay(player.splitHands[0].cards) : '0';
        const hand2Score = player.splitHands[1] ? getScoreDisplay(player.splitHands[1].cards) : '0';
        const htmlPos = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPos}`).text(`Hand 1: ${hand1Score} | Hand 2: ${hand2Score}`);
    }
    function applySplitHandsSettlement(playerIndex, handResults) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const handContainers = cardsContainer.find('.final-hand');
        player.splitHands.forEach((hand, handIndex) => {
            const handContainer = handContainers.eq(handIndex);
            if (handContainer.length > 0 && handResults && handResults[handIndex]) {
                handContainer.removeClass('bust won lost push blackjack twenty-one');
                delete hand.effectClass;
                handContainer.addClass(handResults[handIndex]);
            } else {
                console.warn(`Could not find container for hand ${handIndex}`);
            }
        });
    }


    function updateSplitButtonVisibility() {
        updateButtonStates();
    }



    function createCardElement(card, isHidden = false) {
        if (isHidden) {
            return $('<div class="card card-back"></div>');
        }
        if (!card) {
            console.error('Cannot create card element: card is undefined');
            return $('<div class="card error-card">ERROR</div>');
        }
        if (!card.value || !card.suit || !card.color) {
            console.error('Cannot create card element: card properties are missing', card);
            return $('<div class="card error-card">ERROR</div>');
        }
        const cardElement = $(`
            <div class="card ${card.color}">
                <div class="card-corner top-left">
                    <div class="card-value">${card.value}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
                <div class="card-center">
                    <div class="card-suit-large">${card.suit}</div>
                </div>
                <div class="card-corner bottom-right">
                    <div class="card-value">${card.value}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
            </div>
        `);
        safeSetTimeout(() => {
            cardElement.addClass('dealt');
        }, 100);
        return cardElement;
    }

    function playSound(soundType) {
        if (effectsMuted) return;

        try {
            switch (soundType) {
                case 'cardDeal':
                case 'cardFlip':
                case 'hit':
                    if (dealSound) {
                        dealSound.volume = effectsVolume;
                        dealSound.currentTime = 0;
                        dealSound.play().catch(() => {});
                    }
                    break;
                case 'chipPlace':
                    break;
                default:
                    break;
            }
        } catch (error) {
        }
    }
    function ensureBalanceIntegrity() {
        if (gameState.balance < 0) {
            gameState.balance = 0;
        }
        checkGameOverCondition();
    }

    function checkGameOverCondition() {
        if (gameState.balance === 0 && !gameState.gameInProgress && !gameState.gameOverModalShown) {
            const hasAnyBets = gameState.players.some(player => player.bet > 0);
            if (!hasAnyBets) {
                showGameOverModal();
            }
        }
    }

    function showGameOverModal() {
        if (gameState.gameOverModalShown) {
            return; // Prevent duplicate modals
        }
        gameState.gameOverModalShown = true;
        toggleModal('game-over-modal', true);
    }

    function hideGameOverModal() {
        toggleModal('game-over-modal', false);
    }
    function restartGame() {
        resetBalanceCache();
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.sideBets = { perfectPairs: 0, twentyOnePlusThree: 0 };
            player.splitHands = [];
            player.currentSplitIndex = 0;
        });
        gameState.gameInProgress = false;
        gameState.gameStarted = false;
        gameState.gamePhase = 'waiting';
        gameState.currentTurnIndex = -1;
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;
        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        gameState.gameOverModalShown = false;
        if (gameState.countdownTimer) {
            safeClearInterval(gameState.countdownTimer);
            gameState.countdownTimer = null;
        }
        gameState.countdownActive = false;

        // Clear any pending game over checks to prevent duplicate modals
        activeTimers.timeouts.forEach(timeoutId => {
            clearTimeout(timeoutId);
        });
        activeTimers.timeouts.clear();

        hideGameOverModal();
        updateDisplay();
        updateChipDenominations();
        updateChipButtonStates();
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        hideAllPlayerScores();
        $('.bet-circle').removeClass('has-bet');
        $('.bet-amount').text('');
        $('.bet-chips-stack').empty();
        $('.side-bet-spot').removeClass('has-bet');
        $('.side-bet-amount').text('');
        $('.side-bet-chips-stack').empty();
        $('.player-position, .dealer-section').removeClass('won lost bust push blackjack twenty-one');
        $('.chip-section, .betting-section').show();
        $('.game-actions-section').hide();
        updateButtonStates();
        updateGameStatus(window.i18n ? window.i18n.t('messages.welcomeBack') : 'Welcome back! Place your bets to start playing.');
        startGame();
    }
    function updateDisplay() {
        ensureBalanceIntegrity();
        $('[id^="player-balance"]').text(gameState.balance);

        if (gameState.dealerHiddenCard && gameState.dealerCards.length > 1) {
            $('#dealer-score').text(getScoreDisplay([gameState.dealerCards[0]]));
        } else {
            $('#dealer-score').text(getScoreDisplay(gameState.dealerCards));
        }

        updateChipButtonStates();

        gameState.players.forEach((player, index) => {
            let displayBet = player.bet;
            if (player.hasFreeBet && player.usedFreeDouble) {
                displayBet = player.bet * 2;
            } else if (player.splitHands.length > 0) {
                displayBet = player.bet * 2;
            }

            updateBetDisplay(index, displayBet);

            if (player.splitHands.length === 0) {
                const htmlPos = getPlayerHtmlPosition(index);
                $(`#player-score-${htmlPos}`).text(getScoreDisplay(player.cards));
            }

            const htmlPosition = getPlayerHtmlPosition(index);
            const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);

            betCircle.toggleClass('has-bet', player.bet > 0);
            playerPosition.toggleClass('active', player.isActive);
        });
    }
    function updateBetDisplay(playerIndex, totalBet) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const betAmountElement = $(`#bet-amount-${htmlPosition}`);
        betCircle.find('.bet-chips-stack').remove();
        if (totalBet === 0) {
            betAmountElement.text('0');
            return;
        }
        const formattedAmount = formatBetAmount(totalBet);
        betAmountElement.text(formattedAmount);
        const chipStack = $('<div class="bet-chips-stack"></div>');
        const chips = calculateChipBreakdown(totalBet);
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => a - b);
        const denominations = currentDenominations.length > 0 ? currentDenominations : [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
        chips.forEach((chip, index) => {
            const value = chip.value * 1000;
            const chipClass = getChipPreviewClass(value, denominations);
            const chipElement = $(`<div class="bet-chip-preview ${chipClass}"></div>`);
            // 大金额筹码在下面，小金额筹码在上面
            chipStack.append(chipElement);
        });
        betCircle.append(chipStack);
    }
    function formatBetAmount(amount) {
        return amount.toString();
    }
    function calculateChipBreakdown(totalBet) {
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => b - a);
        let chipValues = currentDenominations.length > 0 ? currentDenominations : [10000, 5000, 1000, 500, 100, 50, 25, 10, 5];
        const chips = [];
        let remaining = totalBet;
        for (let value of chipValues) {
            while (remaining >= value && chips.length < 5) {
                chips.push({ value: value / 1000 });
                remaining -= value;
            }
        }
        return chips;
    }

    function updateChipDenominations() {
        const denominations = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
        const existingChips = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get();
        const needsRecreate = existingChips.length !== denominations.length ||
                             existingChips.some((val, index) => val !== denominations[index]);
        if (needsRecreate) {
            $('.chip-btn').remove();
            const chipTray = $('.chip-tray');
            denominations.forEach((value) => {
                const formattedValue = formatChipValue(value);
                const chipClass = getChipClass(value, denominations);
                const chipButton = $(`
                    <button class="chip-btn" data-value="${value}" title="${formattedValue} chip">
                        <div class="chip ${chipClass}">${formattedValue}</div>
                    </button>
                `);
                chipTray.append(chipButton);
            });
            bindChipEvents();
        }
        updateChipButtonStates();
    }

    function updateChipButtonStates() {
        const balance = gameState.balance;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const gameInProgress = gameState.gameInProgress;
        const gamePhase = gameState.gamePhase;
        const canBet = !gameInProgress && (gamePhase === 'betting' || gamePhase === 'waiting');

        $('.chip-btn').each(function() {
            const chipValue = parseInt($(this).data('value'));
            const canAfford = balance >= chipValue;
            const isEnabled = canBet && canAfford;

            $(this).toggleClass('disabled', !isEnabled).prop('disabled', !isEnabled);

            let title = `${chipValue} chips`;
            if (!canBet) title += ' (Game in progress)';
            else if (!canAfford) title += ' (Insufficient Balance)';

            $(this).attr('title', title);
        });
    }


    function formatChipValue(value) {
        return value.toString();
    }
    function getChipClass(value, denominations = null) {
        if (!denominations) {
            denominations = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
        }
        const chipClasses = ['chip-1', 'chip-2', 'chip-3', 'chip-4', 'chip-5', 'chip-6', 'chip-7', 'chip-8', 'chip-9'];
        const position = denominations.indexOf(value);
        return chipClasses[position % chipClasses.length];
    }

    function getChipPreviewClass(value, denominations = null) {
        if (!denominations) {
            denominations = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
        }
        const chipPreviewClasses = ['chip-1-preview', 'chip-2-preview', 'chip-3-preview', 'chip-4-preview', 'chip-5-preview', 'chip-6-preview', 'chip-7-preview', 'chip-8-preview', 'chip-9-preview'];
        const position = denominations.indexOf(value);
        return chipPreviewClasses[position % chipPreviewClasses.length];
    }
    function bindChipEvents() {
        $(document).off('click.chipEvents').on('click.chipEvents', '.chip-btn', function() {
            if (gameState.gameInProgress || !gameState.gameStarted) return;
            if ($(this).hasClass('disabled') || $(this).prop('disabled')) return;

            const betValue = parseInt($(this).data('value'));
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];



            if (gameState.balance >= betValue && betValue > 0) {
                gameState.balance -= betValue;
                currentPlayer.bet += betValue;
                animateChipToBet(betValue, gameState.currentPlayerIndex);
                updateDisplay();
                safeSetTimeout(() => {
                    updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                    if (currentPlayer.bet > 0 && !gameState.gameInProgress) {
                        showDealButton();
                        updateGameStatus(window.i18n ? window.i18n.t('messages.clickDoneToStart') : 'Click Done to start dealing cards');
                    }
                    updateChipButtonStates();
                    updateButtonStates();
                }, 400);
                $(this).addClass('selected');
                safeSetTimeout(() => $(this).removeClass('selected'), 300);
                playSound('chipPlace');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalance') : 'Insufficient balance!');
            }
        });
    }

    function togglePlayerScore(playerIndex, show) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPosition}`).parent('.player-score').toggleClass('hidden', !show);
    }

    function showPlayerScore(playerIndex) {
        togglePlayerScore(playerIndex, true);
    }

    function hidePlayerScore(playerIndex) {
        togglePlayerScore(playerIndex, false);
    }

    function hideAllPlayerScores() {
        gameState.players.forEach((_, index) => hidePlayerScore(index));
    }
    function displayCards() {
        $('.dealer-cards-area').empty();
        gameState.players.forEach((_, index) => {
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`#player-cards-${htmlPosition}`).empty();
        });
        hideAllPlayerScores();
        gameState.dealerCards.forEach((card, index) => {
            safeSetTimeout(() => {
                if (index === 1 && gameState.gameInProgress && gameState.gamePhase !== 'results') {
                    $('.dealer-cards-area').append(createCardElement(card, true));
                } else {
                    $('.dealer-cards-area').append(createCardElement(card));
                }
                playSound('cardDeal');
            }, index * 100); 
        });
        gameState.players.forEach((player, playerIndex) => {
            player.cards.forEach((card, cardIndex) => {
                const dealDelay = (gameState.dealerCards.length + playerIndex * 2 + cardIndex) * 100;
                safeSetTimeout(() => {
                    const htmlPosition = getPlayerHtmlPosition(playerIndex);
                    $(`#player-cards-${htmlPosition}`).append(createCardElement(card));
                    playSound('cardDeal');
                }, dealDelay);
            });
            if (player.cards.length > 0) {
                const playerLastCardDelay = (gameState.dealerCards.length + playerIndex * 2 + player.cards.length - 1) * 100; 
                safeSetTimeout(() => {
                    showPlayerScore(playerIndex);
                }, playerLastCardDelay + 150); 
            }
        });
    }
    function addCardToPlayer(playerIndex, card) {
        gameState.players[playerIndex].cards.push(card);
        gameState.players[playerIndex].score = calculateScore(gameState.players[playerIndex].cards);
        safeSetTimeout(() => {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            $(`#player-cards-${htmlPosition}`).append(createCardElement(card));
            playSound('cardDeal');
            updateDisplay();
            safeSetTimeout(() => {
                showPlayerScore(playerIndex);
                check21PointEffect(playerIndex);
            }, 100);
        }, 200);
    }
    function startNewGame() {
        if (gameState.gameInProgress || gameState.gamePhase === 'dealing') {
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.bet === 0) {
            updateGameStatus(window.i18n ? window.i18n.t('messages.placeBetFirst') : 'Please place your bet first!');
            return;
        }

        gameState.lastBetAmount = currentPlayer.bet;

        gameState.gamePhase = 'dealing';
        updateSettingsButtonState();
        updateButtonStates();

        if (gameState.deck.length < 10) {
            triggerShuffleWithAnimation().then(() => {
                continueStartNewGame();
            });
            return;
        }
        continueStartNewGame();
    }
    function continueStartNewGame() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        currentPlayer.isActive = true;
        forceSyncSideBetDisplays();
        gameState.dealerCards = [];
        gameState.gameInProgress = true;
        gameState.dealerHiddenCard = null;
        gameState.gamePhase = 'dealing';
        gameState.currentTurnIndex = -1;

        // Initialize Free Bet features for new game
        initializeFreeBetFeatures();

        gameState.players.forEach(player => {
            player.cards = [];
            player.score = 0;
            player.isBust = false;
        });
        hideGameStatus(); 
        hideDealButton();
        $('.chip-section, .betting-section').fadeOut();
        disableGameButtons();
        safeSetTimeout(() => {
            gameState.players.forEach((player) => {
                if (player.isActive) {
                    player.cards.push(dealCard());
                }
            });
            gameState.dealerCards.push(dealCard());
            gameState.players.forEach((player) => {
                if (player.isActive) {
                    player.cards.push(dealCard());
                }
            });
            gameState.dealerCards.push(dealCard());
            gameState.dealerHiddenCard = gameState.dealerCards[1];
            gameState.players.forEach(player => {
                if (player.isActive) {
                    player.score = calculateScore(player.cards);
                }
            });
            gameState.dealerScore = calculateScore([gameState.dealerCards[0]]);
            displayCards();
            updateDisplay();
            safeSetTimeout(() => {
                gameState.players.forEach((player, index) => {
                    if (player.isActive) {
                        showPlayerScore(index);
                        check21PointEffect(index);
                    }
                });
            }, 500);
            safeSetTimeout(() => {
                checkForInsurance();
            }, 500);
        }, 200); 
    }
    function checkForInsurance() {
        if (gameState.dealerCards.length > 0 && gameState.dealerCards[0].value === 'A') {
            offerInsurance();
        } else {
            startPlayerTurns();
        }
    }

    function offerInsurance() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];

        // Check if current player has blackjack
        const hasBlackjack = currentPlayer.score === 21 && currentPlayer.cards.length === 2;

        // Only offer insurance to human player who doesn't have blackjack
        if (!currentPlayer.isAI && !hasBlackjack) {
            const insuranceCost = Math.floor(currentPlayer.bet / 2);

            if (gameState.balance >= insuranceCost) {
                gameState.insuranceOffered = true;
                $('#insurance-cost').text(insuranceCost);
                $('#insurance-panel').fadeIn(300);
                hideGameStatus();
                return; // Wait for player decision
            }
        }

        // Skip insurance for AI players, players with blackjack, or if can't afford
        startPlayerTurns();
    }

    function buyInsurance() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const insuranceCost = Math.floor(currentPlayer.bet / 2);

        gameState.balance -= insuranceCost;
        currentPlayer.hasInsurance = true;
        currentPlayer.insuranceBet = insuranceCost;

        updateDisplay();

        $('#insurance-panel').fadeOut(300);

        setTimeout(() => {
            checkDealerHoleCard();
        }, 500);
    }

    function declineInsurance() {
        $('#insurance-panel').fadeOut(300);
        setTimeout(() => {
            startPlayerTurns();
        }, 300);
    }

    function checkDealerHoleCard() {
        if (gameState.dealerCards.length >= 2) {
            const holeCard = gameState.dealerCards[1]; 
            const holeCardValue = getCardValue(holeCard.value);

            if (holeCardValue === 10) {
                gameState.dealerHasBlackjack = true;
                showGameStatus(window.i18n ? window.i18n.t('messages.checkingDealerCards') : 'Checking dealer cards...');

                setTimeout(() => {
                    revealDealerHiddenCard();
                    setTimeout(() => {
                        handleDealerBlackjack();
                    }, 500);
                }, 1000);
            } else {
                showGameStatus(window.i18n ? window.i18n.t('messages.insuranceLost') : 'Insurance lost - dealer cannot have BlackJack. Continuing game...');
                setTimeout(() => {
                    startPlayerTurns();
                }, 1500);
            }
        } else {
            startPlayerTurns();
        }
    }



    function handleDealerBlackjack() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        let totalWinnings = 0;
        let resultMessage = '';

        if (currentPlayer.hasInsurance) {
            const insurancePayout = currentPlayer.insuranceBet * 2;
            totalWinnings += insurancePayout;
            resultMessage = `Dealer BlackJack! Insurance pays 2:1. You won $${insurancePayout} from insurance.`;
        } else {
            resultMessage = 'Dealer BlackJack!';
        }

        const playerScore = calculateScore(currentPlayer.cards);
        if (playerScore === 21) {
            totalWinnings += currentPlayer.bet;
            if (currentPlayer.hasInsurance) {
                resultMessage += ` Both have BlackJack - original bet returned.`;
            } else {
                resultMessage = 'Both have BlackJack! Push - bet returned.';
            }
        } else {
            if (currentPlayer.hasInsurance) {
                resultMessage += ` You lost your original bet of $${currentPlayer.bet}.`;
            } else {
                resultMessage += ` You lost $${currentPlayer.bet}.`;
            }
        }

        gameState.balance += totalWinnings;
        updateDisplay();

        const continueText = window.i18n ? window.i18n.t('messages.clickToContinue') : ' Click anywhere to continue...';
        showGameStatus(resultMessage + continueText);

        setTimeout(() => {
            const clickHandler = function() {
                $(document).off('click', clickHandler);
                $('.game-table').off('click', clickHandler);
                resetForNextGame();
            };

            $(document).on('click', clickHandler);
            $('.game-table').on('click', clickHandler);
        }, 300);
    }

    function startPlayerTurns() {
        gameState.gamePhase = 'playing';
        updateSettingsButtonState();
        updateButtonStates();
        gameState.currentTurnIndex = 0;
        nextPlayerTurn();
    }
    function nextPlayerTurn() {
        while (gameState.currentTurnIndex < gameState.players.length) {
            const player = gameState.players[gameState.currentTurnIndex];
            if (player.isActive && !player.isBust) {
                if (player.score === 21) {
                    safeSetTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 500);
                    return;
                } else {
                    setActiveTurn(gameState.currentTurnIndex);
                    showGameStatus(window.i18n ? window.i18n.t('messages.yourTurn') : 'Your turn - Hit or Stand?');
                    enableGameButtons();
                    updateSplitButtonVisibility();

                    // Check for Free Bet opportunities (enableGameButtons already calls updateButtonStates)
                    // Only need to check for tutorial hints
                    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                    if (currentPlayer && currentPlayer.cards.length === 2 && (currentPlayer.canFreeDouble || currentPlayer.canFreeSplit)) {
                        let hintMessage = 'Free Bet opportunities available! ';
                        if (currentPlayer.canFreeDouble) {
                            hintMessage += 'Free Double Down available. ';
                        }
                        if (currentPlayer.canFreeSplit) {
                            hintMessage += 'Free Split available. ';
                        }
                        showFreeBetStatus(hintMessage);
                    }

                    return;
                }
            }
            gameState.currentTurnIndex++;
        }
        clearActiveTurn();

        // Check if all players are bust - if so, skip dealer turn and go directly to settlement
        const allPlayersBust = gameState.players.every(player => {
            if (!player.isActive) return true;

            // Check split hands
            if (player.splitHands && player.splitHands.length > 0) {
                return player.splitHands.every(hand => hand.isBust || hand.score > 21);
            }

            // Check regular hand
            return player.isBust || player.score > 21;
        });
        if (allPlayersBust) {
            // All players bust, dealer wins without revealing cards
            showGameStatus(window.i18n ? window.i18n.t('messages.allPlayersBust') : 'All players bust - Dealer wins!');
            setTimeout(() => {
                showGameResults();
            }, 1500);
        } else {
            dealerTurn();
        }
    }



    function getBasicStrategyHint(playerCards, playerScore, dealerUpCard, canDouble, canSplit) {
        const dealerValue = getCardValue(dealerUpCard.value);

        if (canSplit && playerCards.length === 2 && playerCards[0].value === playerCards[1].value) {
            const cardValue = playerCards[0].value;
            if (cardValue === 'A' || cardValue === '8') {
                return 'split';
            }
            if (cardValue === '9' && dealerValue !== 7 && dealerValue !== 10 && dealerValue !== 11) {
                return 'split';
            }
            if ((cardValue === '2' || cardValue === '3' || cardValue === '7') && dealerValue <= 7) {
                return 'split';
            }
            if (cardValue === '6' && dealerValue <= 6) {
                return 'split';
            }
        }

        const hasAce = playerCards.some(card => card.value === 'A');
        if (hasAce && playerScore <= 21) {
            const aceCount = playerCards.filter(card => card.value === 'A').length;
            const otherCardsSum = playerCards.filter(card => card.value !== 'A')
                .reduce((sum, card) => sum + getCardValue(card.value), 0);

            if (aceCount === 1 && otherCardsSum <= 10) {
                if (playerScore >= 19) return 'stand';
                if (playerScore === 18) {
                    if (dealerValue <= 6) return canDouble ? 'double' : 'stand';
                    if (dealerValue === 7 || dealerValue === 8) return 'stand';
                    return 'hit';
                }
                if (playerScore === 17) {
                    if (dealerValue <= 6) return canDouble ? 'double' : 'hit';
                    return 'hit';
                }
                if (playerScore >= 15 && playerScore <= 16) {
                    if (dealerValue <= 6) return canDouble ? 'double' : 'hit';
                    return 'hit';
                }
                if (playerScore >= 13 && playerScore <= 14) {
                    if (dealerValue <= 6) return canDouble ? 'double' : 'hit';
                    return 'hit';
                }
                return 'hit';
            }
        }

        if (playerScore >= 17) return 'stand';
        if (playerScore >= 13 && playerScore <= 16) {
            return dealerValue <= 6 ? 'stand' : 'hit';
        }
        if (playerScore === 12) {
            return (dealerValue >= 4 && dealerValue <= 6) ? 'stand' : 'hit';
        }
        if (playerScore === 11) {
            return canDouble ? 'double' : 'hit';
        }
        if (playerScore === 10) {
            return (dealerValue <= 9 && canDouble) ? 'double' : 'hit';
        }
        if (playerScore === 9) {
            return (dealerValue >= 3 && dealerValue <= 6 && canDouble) ? 'double' : 'hit';
        }

        return 'hit';
    }

    function showHint() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const dealerUpCard = gameState.dealerCards[0];

        const canDouble = currentPlayer.cards.length === 2 && currentPlayer.bet <= gameState.balance;
        const canSplit = canPlayerSplit(gameState.currentPlayerIndex);

        const hint = getBasicStrategyHint(currentPlayer.cards, currentPlayer.score, dealerUpCard, canDouble, canSplit);

        $('.action-btn').removeClass('hint-highlight');

        let hintMessage = '';
        switch(hint) {
            case 'hit':
                $('#hit').addClass('hint-highlight');
                hintMessage = window.i18n ? window.i18n.t('hints.hit') : 'Basic strategy suggests: Hit';
                break;
            case 'stand':
                $('#stand').addClass('hint-highlight');
                hintMessage = window.i18n ? window.i18n.t('hints.stand') : 'Basic strategy suggests: Stand';
                break;
            case 'double':
                if (canDouble) {
                    $('#double-down').addClass('hint-highlight');
                    hintMessage = window.i18n ? window.i18n.t('hints.double') : 'Basic strategy suggests: Double Down';
                } else {
                    $('#hit').addClass('hint-highlight');
                    hintMessage = window.i18n ? window.i18n.t('hints.doubleNotAvailable') : 'Basic strategy suggests: Hit (Double not available)';
                }
                break;
            case 'split':
                if (canSplit) {
                    $('#split').addClass('hint-highlight');
                    hintMessage = window.i18n ? window.i18n.t('hints.split') : 'Basic strategy suggests: Split';
                } else {
                    $('#hit').addClass('hint-highlight');
                    hintMessage = window.i18n ? window.i18n.t('hints.splitNotAvailable') : 'Basic strategy suggests: Hit (Split not available)';
                }
                break;
            default:
                $('#hit').addClass('hint-highlight');
                hintMessage = window.i18n ? window.i18n.t('hints.hit') : 'Basic strategy suggests: Hit';
        }

        updateGameStatus(hintMessage);

        setTimeout(() => {
            $('.action-btn').removeClass('hint-highlight');
            updateGameStatus(window.i18n ? window.i18n.t('messages.yourTurn') : 'Your turn - Hit or Stand?');
        }, 3000);
    }

    function getCardValue(cardValue) {
        switch(cardValue) {
            case 'A': return 11; 
            case 'K':
            case 'Q':
            case 'J': return 10;
            default: return parseInt(cardValue);
        }
    }
    function playerHit() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
                const newCard = dealCard();
                currentHand.cards.push(newCard);
                currentHand.score = calculateScore(currentHand.cards);
                playSound('deal');
                const newCardIndex = currentHand.cards.length - 1;
                displaySplitHands(gameState.currentPlayerIndex, newCardIndex);
                updateDisplay();
                if (currentHand.score > 21) {
                    showBustEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                const message = window.i18n ?
                                    window.i18n.t('messages.handHitOrStand', { handNumber: currentPlayer.currentHandIndex + 1 }) :
                                    `Hand ${currentPlayer.currentHandIndex + 1} - Hit or Stand?`;
                                showGameStatus(message);
                                enableGameButtons();
                                updateSplitButtonVisibility();
                            }
                        });
                    }, 500);
                } else if (currentHand.score === 21) {
                    show21PointEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                const message = window.i18n ?
                                    window.i18n.t('messages.handHitOrStand', { handNumber: currentPlayer.currentHandIndex + 1 }) :
                                    `Hand ${currentPlayer.currentHandIndex + 1} - Hit or Stand?`;
                                showGameStatus(message);
                                enableGameButtons();
                                updateSplitButtonVisibility();
                            }
                        });
                    }, 500); 
                } else {
                    const message = window.i18n ?
                        window.i18n.t('messages.handHitOrStand', { handNumber: currentPlayer.currentHandIndex + 1 }) :
                        `Hand ${currentPlayer.currentHandIndex + 1} - Continue Hit or Stand?`;
                    showGameStatus(message);
                    enableGameButtons();
                    updateSplitButtonVisibility();
                }
            } else {
                addCardToPlayer(gameState.currentPlayerIndex, dealCard());
                if (currentPlayer.score > 21) {
                    setTimeout(() => {
                        showBustEffect(gameState.currentPlayerIndex);
                        setTimeout(() => {
                            gameState.currentTurnIndex++;
                            nextPlayerTurn();
                        }, 500); 
                    }, 500);
                } else if (currentPlayer.score === 21) {
                    const playerPosition = $(`.player-position[data-position="${gameState.currentPlayerIndex}"]`);
                    if (currentPlayer.cards.length === 2) {
                        playerPosition.addClass('blackjack');
                    } else {
                        playerPosition.addClass('twenty-one');
                    }
                    setTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 500);
                } else {
                    showGameStatus(window.i18n ? window.i18n.t('messages.continueHitOrStand') : 'Continue Hit or Stand?');
                    enableGameButtons();
                    updateSplitButtonVisibility();
                }
            }
        }, 300);
    }
    function playerStand() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                    if (!hasMoreHands) {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    } else {
                        const message = window.i18n ?
                            window.i18n.t('messages.handHitOrStand', { handNumber: currentPlayer.currentHandIndex + 1 }) :
                            `Hand ${currentPlayer.currentHandIndex + 1} - Hit or Stand?`;
                        showGameStatus(message);
                        enableGameButtons();
                        updateSplitButtonVisibility();
                    }
                });
            } else {
                gameState.currentTurnIndex++;
                nextPlayerTurn();
            }
        }, 500);
    }
    function revealDealerHiddenCard() {
        if (gameState.dealerCards.length >= 2) {
            const hiddenCardElement = $('.dealer-cards-area .card-back, .dealer-cards-area .card.hidden');
            if (hiddenCardElement.length > 0) {
                const revealedCard = createCardElement(gameState.dealerCards[1]);
                hiddenCardElement.replaceWith(revealedCard);
                playSound('cardFlip');
            }
        }
        gameState.dealerHiddenCard = null;
        gameState.dealerScore = calculateScore(gameState.dealerCards);
        updateDisplay();
    }

    function dealerTurn() {
        gameState.gamePhase = 'dealer';
        updateSettingsButtonState();
        hideGameStatus();
        disableGameButtons();

        setTimeout(() => {
            revealDealerHiddenCard();
            dealerHitSequence();
        }, 200);
    }
    function dealerHitSequence() {
        const shouldDealerHit = shouldDealerHitStandard();
        if (shouldDealerHit) {
            setTimeout(() => {
                gameState.dealerCards.push(dealCard());
                gameState.dealerScore = calculateScore(gameState.dealerCards);
                $('.dealer-cards-area').append(createCardElement(gameState.dealerCards[gameState.dealerCards.length - 1]));
                updateDisplay();
                playSound('cardDeal');
                if (gameState.dealerScore === 21) {
                    if (gameState.dealerCards.length === 2) {
                        showDealerEffect('blackjack');
                    } else {
                        showDealerEffect('twenty-one');
                    }
                }
                if (gameState.dealerScore > 21) {
                    showDealerEffect('bust');
                }
                dealerHitSequence();
            }, 300);
        } else {
            if (gameState.dealerScore > 21) {
                showDealerEffect('bust');
            }
            setTimeout(() => {
                showGameResults();
            }, 500);
        }
    }
    function shouldDealerHitStandard() {
        if (gameState.dealerScore <= 16) {
            return true;
        }
        if (gameState.dealerScore >= 18) {
            return false;
        }
        if (gameState.dealerScore === 17) {
            return isSoft17(gameState.dealerCards);
        }
        return false;
    }
    function isSoft17(cards) {
        if (!cards || cards.length === 0) return false;
        let aces = 0;
        let otherCardsTotal = 0;
        for (let card of cards) {
            if (card.value === 'A') {
                aces++;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                otherCardsTotal += 10;
            } else {
                otherCardsTotal += parseInt(card.value);
            }
        }
        return aces >= 1 && (otherCardsTotal + (aces - 1)) === 6;
    }
    function doubleDown() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (!gameState.gameInProgress ||
            gameState.currentTurnIndex !== gameState.currentPlayerIndex ||
            currentPlayer.cards.length !== 2) {
            return;
        }
        // Check if this is a free double down
        const isFreeDouble = currentPlayer.hasFreeBet && currentPlayer.usedFreeDouble;

        if (!isFreeDouble) {
            // Regular double down - check balance and deduct
            if (currentPlayer.bet > gameState.balance) {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalanceForDouble') : 'Insufficient balance for double down!');
                return;
            }
            gameState.balance -= currentPlayer.bet;
        }

        if (!isFreeDouble) {
            // Only double the bet amount for paid double down
            currentPlayer.bet *= 2;
        } else {
            // For free double, mark the doubled amount for settlement calculation
            currentPlayer.doubledBet = currentPlayer.bet * 2;
        }
        disableGameButtons();
        setTimeout(() => {
            addCardToPlayer(gameState.currentPlayerIndex, dealCard());
            setTimeout(() => {
                if (currentPlayer.score > 21) {
                    showBustEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 300);
                } else {
                    gameState.currentTurnIndex++;
                    nextPlayerTurn();
                }
            }, 300); 
        }, 300);

        // Show appropriate status message
        if (isFreeDouble) {
            updateGameStatus(window.i18n ? window.i18n.t('messages.freeDoubleAvailable') : 'Free Double Down used! One card dealt.');
            showFreeBetStatus(window.i18n ? window.i18n.t('messages.freeDoubleAvailable') : 'Free Double Down activated - you only risk your original bet!');
        } else {
            updateGameStatus(window.i18n ? window.i18n.t('messages.yourTurn') : 'Double down! One card dealt.');
        }

        updateDisplay();
    }
    function showGameResults() {
        gameState.gamePhase = 'results';
        updateGameStatus(window.i18n ? window.i18n.t('messages.calculatingResults') : 'Calculating results...');
        let totalWinnings = 0;
        gameState.winSoundPlayed = false;

        // Check for dealer 22 push rule in Free Bet Blackjack
        const isDealer22Push = checkDealer22Push();
        gameState.players.forEach((player, index) => {
            if (player.isActive) {
                let totalPlayerWinnings = 0;
                let resultClass = '';
                if (player.splitHands.length > 0) {
                    let handResults = [];
                    player.splitHands.forEach((hand) => {
                        let handWinnings = 0;
                        let handResult = '';
                        if (hand.isBust || hand.score > 21) {
                            handResult = 'lost';
                            handWinnings = 0;
                        } else if (hand.score === 21 && hand.cards.length === 2) {
                            handResult = 'blackjack';
                            handWinnings = Math.floor(hand.bet * 2.5);
                        } else if (isDealer22Push && gameState.dealerScore === 22) {
                            // Dealer 22 push rule
                            handResult = 'push';
                            handWinnings = hand.bet;
                        } else if (gameState.dealerScore > 21) {
                            handResult = 'won';
                            if (hand.hasFreeBet && hand.usedFreeDouble) {
                                // Free double win: return original bet + win amount based on original bet
                                // Example: bet 1000, free double -> win 1000 (original) + 1000 (win) = 2000 total
                                handWinnings = hand.bet * 2;
                            } else if (hand.hasFreeBet) {
                                // Regular free bet win: return original bet + win original bet = 2x original bet
                                handWinnings = hand.bet * 2;
                            } else {
                                handWinnings = hand.bet * 2;
                            }
                        } else if (hand.score > gameState.dealerScore) {
                            handResult = 'won';
                            if (hand.hasFreeBet && hand.usedFreeDouble) {
                                // Free double win: return original bet + win amount based on original bet
                                // Example: bet 1000, free double -> win 1000 (original) + 1000 (win) = 2000 total
                                handWinnings = hand.bet * 2;
                            } else if (hand.hasFreeBet) {
                                // Regular free bet win: return original bet + win original bet = 2x original bet
                                handWinnings = hand.bet * 2;
                            } else {
                                handWinnings = hand.bet * 2;
                            }
                        } else if (hand.score === gameState.dealerScore) {
                            handResult = 'push';
                            handWinnings = hand.bet;
                        } else {
                            handResult = 'lost';
                            if (hand.hasFreeBet) {
                                // Free bet loss: no loss for free bet portion
                                handWinnings = 0;
                            } else {
                                handWinnings = 0;
                            }
                        }
                        handResults.push(handResult);
                        totalPlayerWinnings += handWinnings;
                    });
                    if (handResults.every(result => result === 'lost' || result === 'bust')) {
                        resultClass = 'lost';
                    } else if (handResults.some(result => result === 'won' || result === 'blackjack')) {
                        resultClass = 'won';
                    } else if (handResults.every(result => result === 'push')) {
                        resultClass = 'push';
                    } else {
                        resultClass = 'push'; 
                    }
                    setTimeout(() => {
                        applySplitHandsSettlement(index, handResults);
                    }, 300);
                } else {
                    // Free Bet Blackjack settlement logic
                    if (player.score > 21) {
                        resultClass = 'bust lost';
                        totalPlayerWinnings = 0;
                    } else if (player.score === 21 && player.cards.length === 2) {
                        // Blackjack always wins, even against dealer 22
                        resultClass = 'blackjack won';
                        totalPlayerWinnings = Math.floor(player.bet * 2.5);
                    } else if (isDealer22Push && gameState.dealerScore === 22) {
                        // Special rule: Dealer 22 pushes all non-blackjack hands
                        resultClass = 'push';
                        totalPlayerWinnings = player.bet;
                    } else if (gameState.dealerScore > 21) {
                        resultClass = 'won';
                        if (player.hasFreeBet && player.usedFreeDouble) {
                            // Free double win: return original bet + win amount based on original bet
                            // Example: bet 1000, free double -> win 1000 (original) + 1000 (win) = 2000 total
                            totalPlayerWinnings = player.bet * 2;
                        } else if (player.hasFreeBet) {
                            // Regular free bet win: return original bet + win original bet = 2x original bet
                            totalPlayerWinnings = player.bet * 2;
                        } else {
                            totalPlayerWinnings = player.bet * 2;
                        }
                    } else if (player.score > gameState.dealerScore) {
                        resultClass = 'won';
                        if (player.hasFreeBet && player.usedFreeDouble) {
                            // Free double win: return original bet + win amount based on original bet
                            // Example: bet 1000, free double -> win 1000 (original) + 1000 (win) = 2000 total
                            totalPlayerWinnings = player.bet * 2;
                        } else if (player.hasFreeBet) {
                            // Regular free bet win: return original bet + win original bet = 2x original bet
                            totalPlayerWinnings = player.bet * 2;
                        } else {
                            totalPlayerWinnings = player.bet * 2;
                        }
                    } else if (player.score === gameState.dealerScore) {
                        resultClass = 'push';
                        totalPlayerWinnings = player.bet;
                    } else {
                        resultClass = 'lost';
                        if (player.hasFreeBet) {
                            // Free bet loss: only lose original bet, not the free portion
                            totalPlayerWinnings = 0;
                        } else {
                            totalPlayerWinnings = 0;
                        }
                    }
                    const htmlPosition = getPlayerHtmlPosition(index);
                    const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
                    playerPosition.removeClass('bust lost won push blackjack');
                    setTimeout(() => {
                        playerPosition.addClass(resultClass);
                    }, 200); 
                }
                if (index === gameState.currentPlayerIndex) {
                    totalWinnings = totalPlayerWinnings;
                }
            }
        });
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.hasInsurance) {
            if (gameState.dealerScore === 21 && gameState.dealerCards.length === 2) {
                const insurancePayout = currentPlayer.insuranceBet * 2;
                totalWinnings += insurancePayout;
                console.log(`Insurance payout: ${insurancePayout}`);
            }
        }

        if (totalWinnings > 0) {
            gameState.balance += totalWinnings;
            updateDisplay();
        }

        cacheUserBalance();
        setTimeout(() => {
            let dealerWon = false;
            let dealerLost = false;
            gameState.players.forEach((player) => {
                if (player.isActive) {
                    if (player.score <= 21 && (gameState.dealerScore > 21 || player.score > gameState.dealerScore)) {
                        dealerLost = true;
                    } else if (player.score > 21 || (gameState.dealerScore <= 21 && gameState.dealerScore > player.score)) {
                        dealerWon = true;
                    }
                }
            });
            if (!$('.dealer-section').hasClass('blackjack twenty-one bust')) {
                if (dealerLost && !dealerWon) {
                    showDealerEffect('lost');
                } else if (dealerWon && !dealerLost) {
                    showDealerEffect('won');
                }
            }
        }, 200);

        safeSetTimeout(() => {
            showGameResultsAndWaitForClick(totalWinnings);
        }, 300);
    }

    function showGameResultsAndWaitForClick(totalWinnings) {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        let resultMessage = '';
        let netResult = 0;

        if (totalWinnings > currentPlayer.bet) {
            netResult = totalWinnings - currentPlayer.bet;
            resultMessage = `You won $${netResult}!`;
        } else if (totalWinnings === currentPlayer.bet) {
            resultMessage = 'It\'s a push!';
        } else {
            netResult = currentPlayer.bet;
            resultMessage = `You lost $${netResult}!`;
        }

        const continueText = window.i18n ? window.i18n.t('messages.clickToContinue') : ' Click anywhere to continue...';
        showGameStatus(resultMessage + continueText);

        setTimeout(() => {
            const clickHandler = function() {
                $(document).off('click', clickHandler);
                $('.game-table').off('click', clickHandler);
                resetForNextGame();
            };

            $(document).on('click', clickHandler);
            $('.game-table').on('click', clickHandler);
        }, 300); 
    }
    function resetForNextGame() {
        cleanupAnimationElements();



        let cardsDiscarded = 0;

        gameState.dealerCards.forEach(card => {
            if (card) {
                gameState.discardPile.push(card);
                cardsDiscarded++;
            }
        });

        gameState.players.forEach(player => {
            player.cards.forEach(card => {
                if (card) {
                    gameState.discardPile.push(card);
                    cardsDiscarded++;
                }
            });

            if (player.splitHands && player.splitHands.length > 0) {
                player.splitHands.forEach(hand => {
                    hand.cards.forEach(card => {
                        if (card) {
                            gameState.discardPile.push(card);
                            cardsDiscarded++;
                        }
                    });
                });
            }
        });



        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;

        gameState.players.forEach(player => {
            player.cards = [];
            player.score = 0;
            player.splitHands = [];
            player.currentHandIndex = 0;
        });



        if (gameSettings.pendingDeckCount && gameSettings.pendingDeckCount !== gameSettings.deckCount) {
            gameSettings.deckCount = gameSettings.pendingDeckCount;
            gameSettings.pendingDeckCount = null;
            saveDeckSettings();

            createDeck();
            shuffleDeck();

            const plural = gameSettings.deckCount > 1 ? 's' : '';
            const message = window.i18n ?
                window.i18n.t('messages.deckSettingApplied', { deckCount: gameSettings.deckCount, plural: plural }) :
                `Deck setting applied: ${gameSettings.deckCount} deck${plural}`;
            updateGameStatus(message);
        }

        gameState.gameInProgress = false;
        gameState.gamePhase = 'betting';

        // Reset Free Bet global states
        gameState.freeBetActive = false;
        gameState.dealer22Push = false;
        $('#free-bet-status').hide();

        updateSettingsButtonState();
        updateButtonStates();
        gameState.currentTurnIndex = -1;
        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        gameState.insuranceOffered = false;
        gameState.dealerHasBlackjack = false;
        $('.dealer-section').removeClass('bust lost won blackjack twenty-one');
        $('#insurance-panel').hide();
        $('.insurance-stack').remove();

        gameState.players.forEach((player, index) => {
            // Don't reset bet - keep it for next round
            // player.bet = 0;
            player.isActive = false;
            player.cards = [];
            player.score = 0;
            player.isBust = false;
            player.splitHands = [];
            player.currentHandIndex = 0;
            player.canSplit = false;
            player.hasInsurance = false;
            player.insuranceBet = 0;
            player.sideBets = {
                perfectPairs: 0,
                twentyOnePlusThree: 0
            };
            // Reset Free Bet states
            player.hasFreeBet = false;
            player.freeBetAmount = 0;
            player.canFreeDouble = false;
            player.canFreeSplit = false;
            player.usedFreeDouble = false;
            player.usedFreeSplit = false;
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`.player-position[data-position="${htmlPosition}"]`).removeClass('bust lost won push blackjack twenty-one active-turn');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-chips-stack`).remove();
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot`).removeClass('has-bet');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-amount`).text('0');
            // Hide Free Bet indicators
            $(`#free-bet-indicator-${htmlPosition}`).hide().removeClass('active');
        });
        updateDeckDisplay();
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        hideAllPlayerScores();

        // Check for game over condition first
        if (gameState.balance <= 0) {
            // Force balance to exactly 0 to prevent negative values
            if (gameState.balance < 0) {
                gameState.balance = 0;
            }

            // Clear all bets and show game over
            gameState.players.forEach(player => {
                player.bet = 0;
            });
            updateDisplay();

            // Use checkGameOverCondition to avoid duplicate modals
            setTimeout(() => {
                checkGameOverCondition();
            }, 500);
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (gameState.lastBetAmount > 0 &&
            gameState.balance >= gameState.lastBetAmount) {
            gameState.balance -= gameState.lastBetAmount;
            currentPlayer.bet = gameState.lastBetAmount;

            updateChipDenominations();

            setTimeout(() => {
                animateChipToBet(gameState.lastBetAmount, gameState.currentPlayerIndex);
                updateDisplay();
                setTimeout(() => {
                    updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                    updateButtonStates();
                }, 400);
            }, 100);
        } else {
            if (gameState.balance < gameState.lastBetAmount) {
                showGameStatus(window.i18n ? window.i18n.t('messages.placeBetsNextRound') : 'Insufficient balance for previous bet amount. Place your bets for next round');
            } else {
                showGameStatus(window.i18n ? window.i18n.t('messages.placeBetsNextRound') : 'Place your bets for next round');
            }
            updateButtonStates();
        }

        $('.game-actions-section').fadeOut();
        updateDisplay();
        updateChipDenominations();
        updateChipButtonStates();
        $('.chip-section, .betting-section').fadeIn();
        forceSyncSideBetDisplays();
        gameState.gameStarted = true;
        startBettingCountdown();
    }

    function updateGameStatus(message) {
        const gameStatus = domCache.gameStatus || document.getElementById('game-status');
        if (gameStatus) {
            gameStatus.textContent = message;
        }
    }

    function showGameStatus(message) {
        updateGameStatus(message);
        const gameStatus = domCache.gameStatus || document.getElementById('game-status');
        if (gameStatus) {
            gameStatus.classList.remove('hidden');
            gameStatus.classList.add('show');
        }
    }

    function hideGameStatus() {
        const gameStatus = domCache.gameStatus || document.getElementById('game-status');
        if (gameStatus) {
            gameStatus.classList.remove('show');
            gameStatus.classList.add('hidden');
        }
    }
    function startGame() {
        if (gameState.gameStarted) return;

        if (!gameState.hasAutoFullscreened) {
            gameState.hasAutoFullscreened = true;
            autoFullscreen();
        }

        initializePlayers();
        updatePlayerPositionsDisplay();
        gameState.gameStarted = true;
        gameState.gamePhase = 'betting';
        updateSettingsButtonState();
        updateButtonStates();

        $('#game-status').show();
        $('.dealer-section').addClass('show').show();
        $('.players-area').addClass('show').show();
        $('.top-status').show();
        $('.deck-area').show();

        updateChipDenominations();
        updateChipButtonStates();
        updateButtonStates();
        hideDealButton();
        showGameStatus(window.i18n ? window.i18n.t('messages.welcomePlaceBet') : 'Welcome! Please place your bet to start playing (Min: 5).');
        startBettingCountdown();
    }
    function startBettingCountdown() {
        updateDealButtonState();
    }

    function showPlayerEffect(playerIndex, effectType) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);

        if (player.splitHands && player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            const cardsContainer = $(`#player-cards-${htmlPosition}`);
            const currentHandContainer = cardsContainer.find('.current-hand');

            if (effectType === 'bust') {
                currentHand.isBust = true;
                currentHand.effectClass = 'bust';
                currentHandContainer.addClass('bust');
            } else if (effectType === 'twenty-one') {
                currentHand.effectClass = 'twenty-one';
                currentHandContainer.addClass('twenty-one');
            }
        } else {
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);

            if (effectType === 'bust') {
                player.isBust = true;
                playerPosition.addClass('bust');
            } else if (effectType === 'twenty-one') {
                const isNaturalBlackjack = player.cards.length === 2 && calculateScore(player.cards) === 21;
                playerPosition.addClass(isNaturalBlackjack ? 'blackjack' : 'twenty-one');
            }
        }
    }

    function showBustEffect(playerIndex) {
        showPlayerEffect(playerIndex, 'bust');
    }

    function show21PointEffect(playerIndex) {
        showPlayerEffect(playerIndex, 'twenty-one');
    }
    function showDealerEffect(effectType) {
        const dealerSection = $('.dealer-section');
        dealerSection.removeClass('bust lost won blackjack twenty-one');
        dealerSection.addClass(effectType);
    }
    function check21PointEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.score === 21) {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (player.cards.length === 2) {
                playerPosition.addClass('blackjack');
            } else {
                playerPosition.addClass('twenty-one');
            }
        }
    }
    function createFlyingChip(chipClass, startPos, endPos, delay = 0) {
        const chip = $(`<div class="flying-chip ${chipClass}"></div>`);
        chip.css({
            left: startPos.left + 'px',
            top: startPos.top + 'px'
        });
        $('body').append(chip);
        safeSetTimeout(() => {
            chip.css({
                transition: 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                left: endPos.left + 'px',
                top: endPos.top + 'px',
                opacity: 0
            });
            safeSetTimeout(() => {
                chip.css({
                    transform: 'scale(1.2) rotate(180deg)'
                });
            }, 150);
            safeSetTimeout(() => {
                chip.css({
                    transform: 'scale(0.8) rotate(360deg)'
                });
            }, 300);
            safeSetTimeout(() => {
                chip.remove();
            }, 600);
        }, delay);
    }
    function animateChipToBet(chipValue, playerIndex) {
        const chipButton = $(`.chip-btn[data-value="${chipValue}"]`);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        if (chipButton.length && betCircle.length) {
            const startPos = chipButton.offset();
            const endPos = betCircle.offset();
            const denominations = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
            const chipClass = getChipPreviewClass(chipValue, denominations);
            createFlyingChip(chipClass, startPos, endPos);
        }
    }
    function animateSplitBetChips(totalBet, playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const playerAvatar = $(`.player-position[data-position="${htmlPosition}"] .player-avatar`);
        if (!betCircle.length || !playerAvatar.length) return;
        const chips = calculateChipBreakdown(totalBet);
        const endPos = betCircle.offset();
        const startPos = playerAvatar.offset();
        chips.forEach((chip, index) => {
            setTimeout(() => {
                const chipValue = chip.value * 1000; 
                const denominations = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
                const chipClass = getChipPreviewClass(chipValue, denominations);
                const offsetEndPos = {
                    left: endPos.left + (index * 2),
                    top: endPos.top - (index * 2)
                };
                const offsetStartPos = {
                    left: startPos.left + (index * 3),
                    top: startPos.top + (index * 2)
                };
                createFlyingChip(chipClass, offsetStartPos, offsetEndPos);
            }, index * 100); 
        });
    }
    function animateChipToSideBet(chipValue, playerIndex, betType) {
        const chipButton = $(`.side-bet-btn[data-type="${betType}"][data-value="${chipValue}"]`);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const sideBetSpot = $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot[data-type="${betType}"]`);
        if (chipButton.length && sideBetSpot.length) {
            const startPos = chipButton.offset();
            const endPos = sideBetSpot.offset();
            const denominations = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
            const chipClass = getChipPreviewClass(chipValue, denominations);
            createFlyingChip(chipClass, startPos, endPos);
        }
    }

    function setActiveTurn(playerIndex) {
        $('.player-position').removeClass('active-turn');
        if (playerIndex >= 0) {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            $(`.player-position[data-position="${htmlPosition}"]`).addClass('active-turn');
        }
    }
    function clearActiveTurn() {
        $('.player-position').removeClass('active-turn');
    }
    function showActionControls() {
        $('.chip-section, .betting-section').fadeOut();
        $('.game-actions-section').fadeIn();
    }
    function showDealButton() {
        $('#deal-cards').prop('disabled', false);
    }
    function hideDealButton() {
        $('#deal-cards').prop('disabled', true);
    }
    function enableGameButtons() {
        showActionControls();
        $('#hit, #stand').prop('disabled', false);
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        $('#double-down').prop('disabled', currentPlayer.cards.length !== 2 || currentPlayer.bet > gameState.balance);
        $('#split').prop('disabled', !canPlayerSplit(gameState.currentPlayerIndex));


        // Update button states to handle Free Bet button visibility correctly
        updateButtonStates();
    }


    function disableGameButtons() {
        $('.game-actions-section').fadeOut();
        $('#hit, #stand, #double-down').prop('disabled', true);
        if (!gameState.gameInProgress) {
            $('.chip-section, .betting-section').fadeIn();
        }
    }
    function forceSyncSideBetDisplays() {
        gameState.players.forEach((player, index) => {
            if (player.sideBets) {
                updatePlayerSideBetDisplay(index);
            }
        });
    }

    function updatePlayerSideBetDisplay(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player && player.sideBets) {
        }
    }

    function toggleFullscreen() {
        const isFullscreen = !!(document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement);

        if (!isFullscreen) {
            const docEl = document.documentElement;
            const requestFullscreen = docEl.requestFullscreen ||
                                    docEl.webkitRequestFullscreen ||
                                    docEl.mozRequestFullScreen ||
                                    docEl.msRequestFullscreen;

            if (requestFullscreen) {
                requestFullscreen.call(docEl).then(() => {
                    updateFullscreenButton(true);
                    if (typeof setNonGameSectionsVisibility === 'function') {
                        setNonGameSectionsVisibility(false);
                    }
                    setTimeout(lockScreenOrientation, 200);
                }).catch(err => {
                    updateFullscreenButton(false);
                });
            }
        } else {
            const exitFullscreen = document.exitFullscreen ||
                                  document.webkitExitFullscreen ||
                                  document.mozCancelFullScreen ||
                                  document.msExitFullscreen;

            if (exitFullscreen) {
                exitFullscreen.call(document).then(() => {
                    updateFullscreenButton(false);
                    if (typeof setNonGameSectionsVisibility === 'function') {
                        setNonGameSectionsVisibility(true);
                    }
                }).catch(err => {
                    updateFullscreenButton(false);
                });
            }
        }
    }

    function setNonGameSectionsVisibility(shouldShow) {
        try {
            const display = shouldShow ? '' : 'none';
            document.querySelectorAll('.seo-content-section').forEach(el => { el.style.display = display; });
            document.querySelectorAll('.game-recommendations.similar-games').forEach(el => { el.style.display = display; });
            document.querySelectorAll('.game-recommendations.other-games').forEach(el => { el.style.display = display; });
        } catch (_) {}
    }

    function lockScreenOrientation() {
        let targetOrientation = 'landscape';

        if (screen.orientation) {
            const currentOrientation = screen.orientation.type;
            if (currentOrientation.includes('landscape')) {
                targetOrientation = currentOrientation;
            } else {
                targetOrientation = 'landscape-primary';
            }
        }

        if (screen.orientation && screen.orientation.lock) {
            screen.orientation.lock(targetOrientation).catch(err => {
                console.log('Screen orientation lock failed:', err);
            });
        }
        else if (screen.lockOrientation) {
            screen.lockOrientation(targetOrientation);
        }
        else if (screen.mozLockOrientation) {
            screen.mozLockOrientation(targetOrientation);
        }
        else if (screen.msLockOrientation) {
            screen.msLockOrientation(targetOrientation);
        }
    }


    function bindEvents() {
        $('#rules-button').on('click', function() {
            showRulesModal();
        });
        $('#rules-close').on('click', function() {
            hideRulesModal();
        });
        $('#rules-modal').on('click', function(e) {
            if (e.target === this) {
                hideRulesModal();
            }
        });
        $('#settings-button').on('click', function() {
            showUnifiedSettingsModal('freeBetBlackjack', {
                hasBackgroundMusic: false,
                hasEffects: true,
                hasGameSettings: false,
                hasLanguageSwitch: true,
                hasRules: true,
                hasNavigation: true
            });
        });
        $('#settings-close').on('click', function() {
            hideSettingsModal();
        });
        $('#settings-modal').on('click', function(e) {
            if (e.target === this) {
                hideSettingsModal();
            }
        });
        $('.deck-option-btn').on('click', function() {
            $('.deck-option-btn').removeClass('active');
            $(this).addClass('active');
        });
        $('#apply-settings').on('click', function() {
            applySettings();
        });
        $('#restart-game-btn').on('click', function() {
            restartGame();
        });
        $('#game-over-modal').on('click', function(e) {
            if (e.target === this) {
            }
        });
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                if ($('#settings-modal').is(':visible')) {
                    hideSettingsModal();
                } else if ($('#rules-modal').is(':visible')) {
                    hideRulesModal();
                }
            }
        });
        bindChipEvents();
        $('#clear-bet').on('click', function() {
            if (!gameState.gameInProgress && gameState.gameStarted) {
                const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                gameState.balance += currentPlayer.bet;
                currentPlayer.bet = 0;
                updateDisplay();
                updateBetDisplay(gameState.currentPlayerIndex, 0);
                updateGameStatus(window.i18n ? window.i18n.t('messages.betCleared') : 'Bet cleared - Please place new bet');
                hideDealButton();
                updateChipButtonStates();
                updateButtonStates();
                startBettingCountdown();
            }
        });

        $('#double-bet').on('click', function() {
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (!gameState.gameInProgress && currentPlayer.bet > 0 && gameState.gameStarted) {
                const newBetAmount = currentPlayer.bet * 2;
                const additionalBet = newBetAmount - currentPlayer.bet;

                if (gameState.balance >= additionalBet) {
                    gameState.balance -= additionalBet;
                    currentPlayer.bet = newBetAmount;
                    animateChipToBet(additionalBet, gameState.currentPlayerIndex);
                    updateDisplay();
                    safeSetTimeout(() => {
                        updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                        updateChipButtonStates();
                        updateButtonStates();
                    }, 400);
                    playSound('chipPlace');
                } else {
                    updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalanceForDoubleBet') : 'Insufficient balance for double bet!');
                }
            }
        });

        $('#deal-cards').on('click', function() {
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (currentPlayer.bet > 0 && !gameState.gameInProgress && gameState.gameStarted) {
                hideDealButton();
                startNewGame();
            }
        });
        $('.bet-circle').on('click', function() {
            const htmlPosition = parseInt($(this).data('position'));
            let playerIndex = -1;
            for (let i = 0; i < gameSettings.playerCount; i++) {
                if (getPlayerHtmlPosition(i) === htmlPosition) {
                    playerIndex = i;
                    break;
                }
            }
            if (playerIndex === gameState.currentPlayerIndex &&
                gameState.players[playerIndex].bet > 0 &&
                !gameState.gameInProgress &&
                gameState.gamePhase !== 'dealing') {
                startNewGame();
            }
        });
        $('#hint').on('click', showHint);
        $('#hit').on('click', playerHit);
        $('#stand').on('click', playerStand);
        $('#double-down').on('click', doubleDown);
        $('#free-double-down').on('click', function() {
            if (canPlayerFreeDouble(gameState.currentPlayerIndex)) {
                // Mark as free bet before calling original double down
                const player = gameState.players[gameState.currentPlayerIndex];
                player.hasFreeBet = true;
                player.freeBetAmount = player.bet;
                player.usedFreeDouble = true;

                // Call original double down function
                doubleDown();
            }
        });
        $('#split').on('click', function() {
            if (canPlayerSplit(gameState.currentPlayerIndex)) {
                splitPlayerHand(gameState.currentPlayerIndex);
                updateSplitButtonVisibility();
            }
        });
        $('#free-split').on('click', function() {
            if (canPlayerFreeSplit(gameState.currentPlayerIndex)) {
                // Use the original split function but mark as free bet
                const player = gameState.players[gameState.currentPlayerIndex];
                player.hasFreeBet = true;
                player.freeBetAmount = player.bet;
                player.usedFreeSplit = true;

                // Call original split function (it won't deduct balance for free split)
                splitPlayerHand(gameState.currentPlayerIndex);
                updateSplitButtonVisibility();
            }
        });

        $('#buy-insurance').on('click', buyInsurance);
        $('#decline-insurance').on('click', declineInsurance);
        $('.side-bet-btn').on('click', function() {
            if (gameState.gameInProgress || !gameState.gameStarted) return;
            const betType = $(this).data('type');
            const betValue = parseInt($(this).data('value'));
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (gameState.balance >= betValue && betValue > 0) {
                gameState.balance -= betValue;
                currentPlayer.sideBets[betType] += betValue;
                animateChipToSideBet(betValue, gameState.currentPlayerIndex, betType);
                updateDisplay();
                setTimeout(() => {
                    updatePlayerSideBetDisplay(gameState.currentPlayerIndex);
                    $('#perfect-pairs-bet').text(formatBetAmount(currentPlayer.sideBets.perfectPairs));
                    $('#twenty-one-plus-three-bet').text(formatBetAmount(currentPlayer.sideBets.twentyOnePlusThree));
                }, 400);
                $(this).addClass('selected');
                setTimeout(() => $(this).removeClass('selected'), 300);
                playSound('chipPlace');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalance') : 'Insufficient balance!');
            }
        });
        $('#clear-side-bets').on('click', function() {
            if (!gameState.gameInProgress && gameState.gameStarted) {
                const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                Object.values(currentPlayer.sideBets).forEach(amount => {
                    gameState.balance += amount;
                });
                currentPlayer.sideBets = {
                    perfectPairs: 0,
                    twentyOnePlusThree: 0
                };
                updateDisplay();
                updatePlayerSideBetDisplay(gameState.currentPlayerIndex);
                $('#perfect-pairs-bet').text('0');
                $('#twenty-one-plus-three-bet').text('0');
                updateGameStatus(window.i18n ? window.i18n.t('messages.sideBetsCleared') : 'Side bets cleared');
            }
        });
        $(document).on('keydown', function(e) {
            if ($('#result-modal').is(':visible')) return;
            switch(e.key.toLowerCase()) {
                case ' ':
                case 'enter':
                    e.preventDefault();
                    if (!gameState.gameInProgress && gameState.currentBet > 0 && gameState.gamePhase !== 'dealing') {
                        startNewGame();
                    }
                    break;
                case 'h':
                    if (gameState.gameInProgress && !$('#hit').prop('disabled')) {
                        playerHit();
                    }
                    break;
                case 's':
                    if (gameState.gameInProgress && !$('#stand').prop('disabled')) {
                        playerStand();
                    }
                    break;
                case 'd':
                    if (gameState.gameInProgress && !$('#double-down').prop('disabled')) {
                        doubleDown();
                    }
                    break;
                case 'c':
                    $('#clear-bet').click();
                    break;
                case '1':
                    $('.chip-btn[data-value="10000"]').click();
                    break;
                case '2':
                    $('.chip-btn[data-value="20000"]').click();
                    break;
                case '3':
                    $('.chip-btn[data-value="50000"]').click();
                    break;
                case '4':
                    $('.chip-btn[data-value="100000"]').click();
                    break;
                case '5':
                    $('.chip-btn[data-value="250000"]').click();
                    break;
                case '6':
                    $('.chip-btn[data-value="500000"]').click();
                    break;
                case '7':
                    $('.chip-btn[data-value="1000000"]').click();
                    break;
                // Debug keys for Free Bet testing (only in debug mode)

            }
        });
    }
    function initAchievements() {
        loadUserBalance();
        updateDisplay();
    }
    initDOMCache();
    initGame();
    disableGameButtons();
    initAchievements();
    updateButtonStates();
    $('.game-actions-section').hide();
    $('.chip-section, .betting-section').show();
    $('#loading-overlay').fadeOut(1000);



    setTimeout(function() {
        $('#settings-button').off('click').on('click', function() {
            if (!$(this).hasClass('disabled') && !$(this).prop('disabled')) {
                showUnifiedSettingsModal('freeBetBlackjack', {
                    hasBackgroundMusic: false,
                    hasEffects: true,
                    hasGameSettings: false,
                    hasLanguageSwitch: true,
                    hasRules: true,
                    hasNavigation: true
                });
            }
        });

        $('#settings-close').off('click').on('click', function() {
            hideSettingsModal();
        });

        $('#settings-modal').off('click').on('click', function(e) {
            if (e.target === this) {
                hideSettingsModal();
            }
        });

        $('.deck-option-btn').off('click').on('click', function() {
            $('.deck-option-btn').removeClass('active');
            $(this).addClass('active');
        });

        $('#apply-settings').off('click').on('click', function() {
            applySettings();
        });

        updateSettingsButtonState();
    }, 1000);

    $(window).on('beforeunload', function() {
        clearAllTimers();
        cleanupAnimationElements();
    });





    window.resetBalance = resetBalance;
    window.ensureBalanceIntegrity = ensureBalanceIntegrity;

    function toggleModal(modalId, show) {
        const modal = $(`#${modalId}`);
        if (show) {
            modal.addClass('show').css('display', 'flex').hide().fadeIn(300);
            $('body').css('overflow', 'hidden');
        } else {
            modal.fadeOut(300, function() {
                $(this).removeClass('show');
            });
            $('body').css('overflow', 'auto');
        }
    }

    function showRulesModal() {
        toggleModal('rules-modal', true);
    }

    function hideRulesModal() {
        toggleModal('rules-modal', false);
    }

    // 暴露到全局作用域
    window.showRulesModal = showRulesModal;
    window.hideRulesModal = hideRulesModal;

    function loadUserBalance() {
        const savedBalance = localStorage.getItem('blackjack-balance');
        if (savedBalance) {
            try {
                const balance = parseInt(savedBalance);
                if (balance === 500000) {
                    resetBalanceCache();
                    return;
                }
                if (balance > 0) {
                    gameState.balance = balance;
                    gameState.balanceCache = balance;
                }
            } catch (error) {}
        }
    }
    function cacheUserBalance() {
        gameState.balanceCache = gameState.balance;
        localStorage.setItem('blackjack-balance', gameState.balance.toString());
    }

    function resetBalanceCache() {
        gameState.balance = 1000;
        gameState.balanceCache = 1000;
        localStorage.setItem('blackjack-balance', '1000');
    }

    function loadDeckSettings() {
        try {
            const savedDeckCount = localStorage.getItem('freeBetBlackjack-deck-count');
            if (savedDeckCount) {
                const deckCount = parseInt(savedDeckCount);
                if (deckCount === 1 || deckCount === 6) {
                    gameSettings.deckCount = deckCount;
                }
            }
        } catch (error) {}
    }

    function saveDeckSettings() {
        try {
            localStorage.setItem('freeBetBlackjack-deck-count', gameSettings.deckCount.toString());
        } catch (error) {}
    }

    function canModifySettings() {
        return gameState.gamePhase === 'betting' || gameState.gamePhase === 'waiting' || !gameState.gameStarted;
    }

    function updateSettingsButtonState() {
        const canModify = canModifySettings();
        const $settingsButton = $('#settings-button');

        if (canModify) {
            $settingsButton.removeClass('disabled').prop('disabled', false);
            $settingsButton.attr('title', 'Game Settings');
        } else {
            $settingsButton.addClass('disabled').prop('disabled', true);
            $settingsButton.attr('title', 'Settings can only be changed during betting phase');
        }
    }
    function resetBalance() {
        gameState.balance = 1000;
        updateDisplay();
    }
    function updateDealButtonState() {
        const humanPlayer = gameState.players[gameState.currentPlayerIndex];
        if (humanPlayer.bet > 0) {
            const message = window.i18n ?
                window.i18n.t('messages.betPlaced', { amount: humanPlayer.bet }) :
                'Bet placed: ' + humanPlayer.bet;
            updateGameStatus(message);
        } else {
            updateGameStatus(window.i18n ? window.i18n.t('messages.placeBetToStart') : 'Place your bet to start playing');
        }
    }

    function showSettingsModal() {
        if (!canModifySettings()) {
            updateGameStatus(window.i18n ? window.i18n.t('messages.modifySettingsDuringBetting') : 'Please modify the settings during the betting phase');
            return;
        }
        updateSettingsDisplay();
        toggleModal('settings-modal', true);
    }

    function hideSettingsModal() {
        toggleModal('settings-modal', false);
    }

    function updateSettingsDisplay() {
        const currentDeckText = gameSettings.deckCount === 1 ? '1 Deck' : '6 Decks';
        $('#current-deck-count').text(currentDeckText);

        $('.deck-option-btn').removeClass('active');
        const targetDeckCount = gameSettings.pendingDeckCount || gameSettings.deckCount;
        $(`.deck-option-btn[data-decks="${targetDeckCount}"]`).addClass('active');

        if (gameSettings.pendingDeckCount && gameSettings.pendingDeckCount !== gameSettings.deckCount) {
            $('#settings-notice').show();
        } else {
            $('#settings-notice').hide();
        }
    }

    function applySettings() {
        const selectedDecks = parseInt($('.deck-option-btn.active').data('decks'));

        if (selectedDecks !== gameSettings.deckCount) {
            if (canModifySettings()) {
                gameSettings.deckCount = selectedDecks;
                gameSettings.pendingDeckCount = null;
                saveDeckSettings();
                resetGameForNewSettings();
                const plural = selectedDecks > 1 ? 's' : '';
                const message = window.i18n ?
                    window.i18n.t('messages.gameResetWithDecks', { deckCount: selectedDecks, plural: plural }) :
                    `Game reset with ${selectedDecks} deck${plural}`;
                updateGameStatus(message);
            } else {
                gameSettings.pendingDeckCount = selectedDecks;
                updateGameStatus(window.i18n ? window.i18n.t('messages.settingWillTakeEffect') : 'The setting will take effect in the next round');
            }
        }

        hideSettingsModal();
    }

    function resetGameForNewSettings() {
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.splitHands = [];
            player.currentSplitIndex = 0;
            player.isBust = false;

            player.hasInsurance = false;
            player.insuranceBet = 0;
        });

        gameState.gameInProgress = false;
        gameState.gamePhase = 'waiting';
        gameState.currentTurnIndex = -1;
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;
        gameState.insuranceOffered = false;
        gameState.dealerHasBlackjack = false;

        createDeck();
        shuffleDeck();
        updateDisplay();
        hideAllPlayerScores();

        updateButtonStates();
        $('#insurance-panel').hide();

        clearAllCards();
        startGame();
    }

    function clearAllCards() {
        $('#dealer-cards').empty();
        $('.player-cards-area').empty();
        $('.bet-amount').text('0');
    }

    function updateButtonStates() {
        const gamePhase = gameState.gamePhase;
        const gameInProgress = gameState.gameInProgress;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const hasActiveBet = currentPlayer && currentPlayer.bet > 0;

        updateChipButtonStates();

        const canClearBet = !gameInProgress && hasActiveBet && (gamePhase === 'betting' || gamePhase === 'waiting');
        const canDeal = !gameInProgress && hasActiveBet && (gamePhase === 'betting' || gamePhase === 'waiting');
        const canDoubleBet = !gameInProgress && hasActiveBet && (gamePhase === 'betting' || gamePhase === 'waiting') &&
                           gameState.balance >= currentPlayer.bet;

        $('#clear-bet').prop('disabled', !canClearBet).toggleClass('disabled', !canClearBet);
        $('#double-bet').prop('disabled', !canDoubleBet).toggleClass('disabled', !canDoubleBet);
        $('#deal-cards').prop('disabled', !canDeal).toggleClass('disabled', !canDeal);



        const isPlayerTurn = gamePhase === 'playing' && gameState.currentTurnIndex === gameState.currentPlayerIndex;
        const canHint = gameInProgress || isPlayerTurn;
        const canHit = isPlayerTurn && currentPlayer && currentPlayer.score < 21;
        const canStand = isPlayerTurn;
        const canDouble = isPlayerTurn && currentPlayer && currentPlayer.cards.length === 2 && gameState.balance >= currentPlayer.bet;
        const canSplit = isPlayerTurn && currentPlayer && canPlayerSplit(gameState.currentPlayerIndex);

        // Free Bet Blackjack specific button states
        const canFreeDouble = isPlayerTurn && currentPlayer && canPlayerFreeDouble(gameState.currentPlayerIndex);
        const canFreeSplit = isPlayerTurn && currentPlayer && canPlayerFreeSplit(gameState.currentPlayerIndex);

        // Check if split is available but not free (10-value cards)
        const canPaidSplit = canSplit && !canFreeSplit;

        $('#hint').prop('disabled', !canHint).toggleClass('disabled', !canHint);
        $('#hit').prop('disabled', !canHit).toggleClass('disabled', !canHit);
        $('#stand').prop('disabled', !canStand).toggleClass('disabled', !canStand);
        $('#double-down').prop('disabled', !canDouble).toggleClass('disabled', !canDouble);
        $('#split').prop('disabled', !canSplit).toggleClass('disabled', !canSplit);

        // Update Free Bet buttons
        $('#free-double-down').prop('disabled', !canFreeDouble).toggleClass('disabled', !canFreeDouble);
        $('#free-split').prop('disabled', !canFreeSplit).toggleClass('disabled', !canFreeSplit);

        // Show/hide Free Bet buttons based on availability
        if (canFreeDouble) {
            $('#free-double-down').show();
            $('#double-down').hide();
        } else {
            $('#free-double-down').hide();
            $('#double-down').show();
        }

        if (canFreeSplit) {
            $('#free-split').show();
            $('#split').hide();
        } else if (canPaidSplit) {
            $('#free-split').hide();
            $('#split').show();
        } else {
            $('#free-split').hide();
            $('#split').hide();
        }
    }

    // Free Bet Blackjack Core Functions

    function canPlayerFreeDouble(playerIndex) {
        const player = gameState.players[playerIndex];
        if (!player || player.cards.length !== 2 || player.usedFreeDouble) {
            return false;
        }

        // Free double available on hard 9, 10, or 11
        const score = calculateScore(player.cards);
        const hasAce = player.cards.some(card => card.value === 'A');
        const isHardTotal = !hasAce || score > 11;

        return isHardTotal && (score === 9 || score === 10 || score === 11);
    }

    function canPlayerFreeSplit(playerIndex) {
        const player = gameState.players[playerIndex];
        if (!player || player.cards.length !== 2 || player.usedFreeSplit) {
            return false;
        }

        const card1Value = player.cards[0].value;
        const card2Value = player.cards[1].value;
        const isSameValue = card1Value === card2Value;

        // Free split available only for pairs A-9 (not 10, J, Q, K)
        const is10ValueCard = ['10', 'J', 'Q', 'K'].includes(card1Value);

        return isSameValue && !is10ValueCard;
    }







    function showFreeBetStatus(message) {
        const statusElement = $('#free-bet-status');
        const messageElement = statusElement.find('.free-bet-message');

        messageElement.text(message);
        statusElement.fadeIn(300);

        setTimeout(() => {
            statusElement.fadeOut(300);
        }, 3000);
    }

    function checkDealer22Push() {
        if (gameState.dealerScore === 22) {
            gameState.dealer22Push = true;

            // Show special notification
            const notification = $('<div class="dealer-22-notification">Dealer 22 - All non-Blackjack hands push!</div>');
            $('.table-center').append(notification);

            setTimeout(() => {
                notification.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 3000);

            return true;
        }
        return false;
    }



    // Initialize Free Bet features when game starts
    function initializeFreeBetFeatures() {
        // Reset all Free Bet states
        gameState.freeBetActive = false;
        gameState.dealer22Push = false;

        // Reset player Free Bet states
        gameState.players.forEach(player => {
            player.hasFreeBet = false;
            player.freeBetAmount = 0;
            player.canFreeDouble = false;
            player.canFreeSplit = false;
            player.usedFreeDouble = false;
            player.usedFreeSplit = false;
        });

        // Hide all Free Bet indicators
        $('.free-bet-indicator').hide().removeClass('active');
        $('#free-bet-status').hide();
    }

    function autoFullscreen() {
        if (!document.fullscreenElement &&
            !document.webkitFullscreenElement &&
            !document.mozFullScreenElement &&
            !document.msFullscreenElement) {

            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen().catch(() => {});
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }
    }

    // 语言切换功能现在由统一设置弹窗处理
    function setupLanguageSwitcher() {
        // 语言切换现在由统一设置弹窗处理
    }

    // 初始化语言切换
    setupLanguageSwitcher();

    // Global functions for unified settings modal
    window.toggleEffects = function() {
        effectsMuted = !effectsMuted;
        updateAudioVolumes();
        saveAudioSettings();
    };

    window.setEffectsVolume = function(volume) {
        effectsVolume = volume;
        updateAudioVolumes();
        saveAudioSettings();
    };

    window.isEffectsMuted = function() {
        return effectsMuted;
    };

    window.isEffectsEnabled = function() {
        return !effectsMuted;
    };

    window.getEffectsVolume = function() {
        return effectsVolume;
    };

    window.testEffects = function() {
        if (dealSound && !effectsMuted) {
            dealSound.currentTime = 0;
            dealSound.play().catch(() => {});
        }
    };

    window.toggleFullscreen = function() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(() => {});
        } else {
            document.exitFullscreen().catch(() => {});
        }
    };

    // Sync top-right fullscreen button state if present
    function updateFullscreenButton(isFullscreen) {
        try {
            const $icon = $('#fullscreen-icon');
            if ($icon.length) {
                $icon.text('⛶');
            }
            const $btn = $('#fullscreen-button');
            if ($btn.length) {
                $btn.attr('title', isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen');
            }
        } catch (_) {}
    }

    function handleFullscreenChange() {
        const isFullscreen = !!(document.fullscreenElement ||
                                document.webkitFullscreenElement ||
                                document.mozFullScreenElement ||
                                document.msFullscreenElement);
        updateFullscreenButton(isFullscreen);
        setNonGameSectionsVisibility(!isFullscreen);
        ensureFullScreenCoverage();
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    updateFullscreenButton(!!document.fullscreenElement);

    function saveAudioSettings() {
        try {
            localStorage.setItem('blackjack-effects-volume', effectsVolume.toString());
            localStorage.setItem('blackjack-effects-muted', effectsMuted.toString());
        } catch (error) {
            console.warn('Failed to save audio settings:', error);
        }
    }

    window.setDeckCount = function(deckCount) {
        const selectedDecks = parseInt(deckCount);
        if (selectedDecks !== gameSettings.deckCount) {
            if (canModifySettings()) {
                gameSettings.deckCount = selectedDecks;
                gameSettings.pendingDeckCount = null;
                saveDeckSettings();
                resetGameForNewSettings();
                const plural = selectedDecks > 1 ? 's' : '';
                const message = window.i18n ?
                    window.i18n.t('messages.gameResetWithDecks', { deckCount: selectedDecks, plural: plural }) :
                    `Game reset with ${selectedDecks} deck${plural}`;
                updateGameStatus(message);
            } else {
                gameSettings.pendingDeckCount = selectedDecks;
                updateGameStatus(window.i18n ? window.i18n.t('messages.settingWillTakeEffect') : 'The setting will take effect in the next round');
            }
        }
    };

    window.getCurrentDeckCount = function() {
        return gameSettings.deckCount;
    };

    // Auto-apply cached settings on page load
    if (typeof window.autoApplyGameSettings === 'function') {
        window.autoApplyGameSettings('freeBetBlackjack');
    }

});
